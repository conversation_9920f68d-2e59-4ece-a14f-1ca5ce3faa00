package io.github.humbleui.skija.svg;

import org.jetbrains.annotations.ApiStatus;

public enum SVGTag {
    CIRCLE,
    CLIP_PATH,
    DEFS,
    ELLIPSE,
    FE_BLEND,
    FE_COLOR_MATRIX,
    FE_COMPOSITE,
    FE_DIFFUSE_LIGHTING,
    FE_DISPLACEMENT_MAP,
    FE_DISTANT_LIGHT,
    FE_FLOOD,
    FE_GAUSSIAN_BLUR,
    FE_IMAGE,
    FE_MORPHOLOGY,
    FE_OFFSET,
    FE_POINT_LIGHT,
    FE_SPECULAR_LIGHTING,
    FE_SPOT_LIGHT,
    FE_TURBULENCE,
    FILTER,
    G,
    IMAGE,
    LIN<PERSON>,
    LINEAR_GRADIENT,
    MASK,
    PATH,
    PATTERN,
    <PERSON>OLYGON,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    RA<PERSON><PERSON>_GRADIENT,
    RECT,
    STOP,
    SVG,
    TEXT,
    TEXT_LITERAL,
    TEXTPATH,
    TSPAN,
    USE;

    @ApiStatus.Internal public static final SVGTag[] _values = values();
}
