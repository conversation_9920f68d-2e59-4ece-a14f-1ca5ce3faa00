package cn.cool.cherish.injection;

import cn.cool.cherish.Client;
import cn.cool.cherish.injection.mixin.client.MixinKeyboard;
import cn.cool.cherish.injection.mixin.client.MixinMinecraft;
import cn.cool.cherish.injection.mixin.client.MixinWindow;
import cn.cool.cherish.injection.mixin.network.MixinConnection;
import cn.cool.cherish.injection.mixin.player.MixinPlayer;
import cn.cool.cherish.injection.mixin.player.MixinTimer;
import cn.cool.cherish.injection.mixin.render.MixinRenderSystem;
import cn.cool.cherish.utils.NativeUtils;
import cn.cool.cherish.injection.base.Transformer;
import cn.cool.cherish.utils.wrapper.IMinecraft;
import lombok.experimental.UtilityClass;

import java.util.HashMap;
import java.util.Map;

/**
 * MixinLoader
 * <AUTHOR>
 */
@UtilityClass
public class MixinLoader implements IMinecraft {
    private static boolean loaded = false;

    public static Map<String, byte[]> originalClassBytesMap;
    public static Map<String, byte[]> classBytesMap;
    public static Transformer transformer;

    public static void init() {
        if (loaded) return;
        loaded = true;

        initializeTransformer();
        performClassTransformation();
        Client.logger.info("Mixin loaded");
    }

    private static void initializeTransformer() {
        originalClassBytesMap = new HashMap<>();
        transformer = new Transformer();

        try {
            Class.forName("net.minecraft.client.Minecraft");
            Class.forName("net.minecraftforge.versions.forge.ForgeVersion");

            transformer.addTransformer(MixinMinecraft.class);
            transformer.addTransformer(MixinConnection.class);
            transformer.addTransformer(MixinKeyboard.class);
            transformer.addTransformer(MixinTimer.class);
            transformer.addTransformer(MixinWindow.class);
            transformer.addTransformer(MixinPlayer.class);
            transformer.addTransformer(MixinRenderSystem.class);

            classBytesMap = transformer.transform();
        } catch (ClassNotFoundException ex) {
            Client.logger.error("Required classes not found for ASM injection");
        }
    }

    private static void performClassTransformation() {
        int successCount = 0;

        for (Map.Entry<String, byte[]> entry : classBytesMap.entrySet()) {
            if (transformClass(entry.getKey(), entry.getValue())) {
                successCount++;
            }
        }

        Client.logger.info("ASM inject success. Rewritten {} classes.", successCount);
    }

    private static boolean transformClass(String className, byte[] newBytes) {
        try {
            Class<?> clazz = Class.forName(className);
            byte[] originalBytes = NativeUtils.Z2V0Q2xhc3Nlc0J5dGVz(clazz);

            originalClassBytesMap.put(className, originalBytes);
            NativeUtils.cmVkZWZpbmVDbGFzc2Vz(clazz, newBytes);

            return true;
        } catch (Exception ex) {
            Client.logger.error("Failed to transform class: {} - {}", className, ex.getMessage());
            return false;
        }
    }
}
