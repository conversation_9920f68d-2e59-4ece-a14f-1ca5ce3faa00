package cn.cool.cherish.injection.base.impl;

import cn.cool.cherish.injection.base.annotation.Inject;
import cn.cool.cherish.injection.base.utils.mapping.Mapping;
import cn.cool.cherish.injection.base.utils.operations.Operation;
import lombok.experimental.UtilityClass;
import org.objectweb.asm.Opcodes;
import org.objectweb.asm.Type;
import org.objectweb.asm.tree.*;

import java.lang.reflect.Method;

@UtilityClass
public class InjectHandler {
    public void handleInject(Method method, ClassNode targetNode, Class<?> targetClass, Class<?> transformerClass) {
        Inject inject = method.getAnnotation(Inject.class);
        String mappedName = Mapping.get(targetClass, inject.method(), inject.desc());
        MethodNode targetMethod = Operation.findTargetMethod(targetNode.methods, mappedName, inject.desc());

        if (targetMethod == null) {
            System.err.println("Target method not found: " + mappedName + " " + inject.desc());
            return;
        }

        InsnList injectionInstructions = createInjectionInstructions(targetMethod, method, transformerClass);

        switch (inject.at()) {
            case HEAD ->
                    targetMethod.instructions.insert(injectionInstructions);
            case TAIL, RETURN -> {
                for (AbstractInsnNode cnm : targetMethod.instructions.toArray()) {
                    if (cnm.getOpcode() >= Opcodes.IRETURN && cnm.getOpcode() <= Opcodes.RETURN) {
                        InsnList newInstructions = createInjectionInstructions(targetMethod, method, transformerClass);
                        targetMethod.instructions.insertBefore(cnm, newInstructions);
                    }
                }
            }
        }
    }

    public InsnList createInjectionInstructions(MethodNode targetMethod, Method injectMethod, Class<?> transformerClass) {
        InsnList instructions = new InsnList();

        Type[] targetParamTypes = Type.getArgumentTypes(targetMethod.desc);
        Type[] injectParamTypes = Type.getArgumentTypes(Type.getMethodDescriptor(injectMethod));

        boolean isTargetStatic = (targetMethod.access & Opcodes.ACC_STATIC) != 0;
        int paramIndex = isTargetStatic ? 0 : 1;

        for (int i = 0; i < injectParamTypes.length; i++) {
            if (i == 0 && !isTargetStatic) {
                instructions.add(new VarInsnNode(Opcodes.ALOAD, 0));
            } else {
                int targetParamIndex = isTargetStatic ? i : i - 1;

                if (targetParamIndex < targetParamTypes.length) {
                    Type paramType = targetParamTypes[targetParamIndex];
                    int loadOpcode = paramType.getOpcode(Opcodes.ILOAD);
                    instructions.add(new VarInsnNode(loadOpcode, paramIndex));
                    paramIndex += paramType.getSize();
                } else {
                    break;
                }
            }
        }

        instructions.add(new MethodInsnNode(
                Opcodes.INVOKESTATIC,
                Type.getInternalName(transformerClass),
                injectMethod.getName(),
                Type.getMethodDescriptor(injectMethod),
                false
        ));

        return instructions;
    }
}
