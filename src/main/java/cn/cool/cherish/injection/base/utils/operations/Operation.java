package cn.cool.cherish.injection.base.utils.operations;

import org.objectweb.asm.tree.MethodNode;

import java.util.List;

public interface Operation {
    static MethodNode findTargetMethod(List<MethodNode> list, String name, String desc) {
        desc = DescParser.mapDesc(desc);
        String finalDesc = desc;
        return list.stream().filter(m -> m.name.equals(name) && m.desc.equals(finalDesc)).findFirst().orElse(null);
    }
}
