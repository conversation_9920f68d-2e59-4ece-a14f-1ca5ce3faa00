package cn.cool.cherish.injection.base.impl;

import cn.cool.cherish.injection.base.annotation.Overwrite;
import cn.cool.cherish.injection.base.utils.mapping.Mapping;
import lombok.experimental.UtilityClass;
import org.objectweb.asm.Opcodes;
import org.objectweb.asm.Type;
import org.objectweb.asm.tree.*;

import java.lang.reflect.Method;
import java.lang.reflect.Modifier;

@UtilityClass
public class OverwriteHandler {
    public void handleOverwrite(Method method, ClassNode targetNode, Class<?> targetClass, Class<?> transformerClass) {
        Overwrite overwrite = method.getAnnotation(Overwrite.class);
        String mappedName = Mapping.get(targetClass, overwrite.method(), overwrite.desc());

        for (int i = 0; i < targetNode.methods.size(); i++) {
            MethodNode methodNode = targetNode.methods.get(i);
            if (methodNode.name.equals(mappedName) && methodNode.desc.equals(overwrite.desc())) {
                targetNode.methods.set(i, createOverwriteMethod(method, methodNode, transformerClass));
                break;
            }
        }
    }

    public MethodNode createOverwriteMethod(Method method, MethodNode original, Class<?> transformerClass) {
        MethodNode newMethod = new MethodNode(original.access, original.name, original.desc,
                original.signature, original.exceptions.toArray(new String[0]));

        Type[] paramTypes = Type.getArgumentTypes(original.desc);
        int paramIndex = Modifier.isStatic(original.access) ? 0 : 1;

        if (!Modifier.isStatic(original.access)) {
            newMethod.instructions.add(new VarInsnNode(Opcodes.ALOAD, 0));
        }

        for (Type paramType : paramTypes) {
            newMethod.instructions.add(new VarInsnNode(paramType.getOpcode(Opcodes.ILOAD), paramIndex));
            paramIndex += paramType.getSize();
        }

        newMethod.instructions.add(new MethodInsnNode(Opcodes.INVOKESTATIC,
                Type.getInternalName(transformerClass), method.getName(), Type.getMethodDescriptor(method)));

        Type returnType = Type.getReturnType(original.desc);
        newMethod.instructions.add(new InsnNode(returnType.getOpcode(Opcodes.IRETURN)));

        return newMethod;
    }
}
