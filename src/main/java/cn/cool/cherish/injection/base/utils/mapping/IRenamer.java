/*
 * Copyright (c) Forge Development LLC and contributors
 * SPDX-License-Identifier: LGPL-2.1-only
 */
package cn.cool.cherish.injection.base.utils.mapping;

public interface IRenamer {
    default String rename(IMappingFile.IPackage value) {
        return value.getMapped();
    }

    default String rename(IMappingFile.IClass value) {
        return value.getMapped();
    }

    default String rename(IMappingFile.IField value) {
        return value.getMapped();
    }

    default String rename(IMappingFile.IMethod value) {
        return value.getMapped();
    }

    default String rename(IMappingFile.IParameter value) {
        return value.getMapped();
    }
}
