package cn.cool.cherish.injection.base.impl;

import cn.cool.cherish.injection.base.annotation.ASM;
import cn.cool.cherish.injection.base.utils.mapping.Mapping;
import cn.cool.cherish.injection.base.utils.operations.Operation;
import lombok.experimental.UtilityClass;
import org.objectweb.asm.tree.ClassNode;
import org.objectweb.asm.tree.MethodNode;

import java.lang.reflect.Method;

@UtilityClass
public class ASMHandler {
    public void handleASM(Method method, ClassNode targetNode, Class<?> targetClass, Class<?> transformerClass) {
        ASM asm = method.getAnnotation(ASM.class);
        String mappedName = Mapping.get(targetClass, asm.method(), asm.desc());

        MethodNode targetMethod = Operation.findTargetMethod(targetNode.methods, mappedName, asm.desc());

        if (targetMethod == null) {
            System.err.println("Target method not found for ASM: " + mappedName + " " + asm.desc());
            return;
        } else {
            System.out.println("Found target method: " + targetMethod.name + " " + targetMethod.desc);
        }

        try {
            method.setAccessible(true);

            if (method.getParameterCount() == 1 && method.getParameterTypes()[0] == MethodNode.class) {
                Object transformerInstance = transformerClass.getDeclaredConstructor().newInstance();

                int originalMaxStack = targetMethod.maxStack;
                int originalMaxLocals = targetMethod.maxLocals;

                method.invoke(transformerInstance, targetMethod);

                targetMethod.maxStack = Math.max(targetMethod.maxStack, originalMaxStack + 2);
                targetMethod.maxLocals = Math.max(targetMethod.maxLocals, originalMaxLocals);
            } else {
                System.err.println("ASM method " + method.getName() + " should have exactly 1 parameter of type MethodNode");
            }

        } catch (Exception e) {
            System.err.println("Error handling ASM method: " + method.getName());
        }
    }
}
