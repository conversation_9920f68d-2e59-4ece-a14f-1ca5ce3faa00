package cn.cool.cherish.injection.base.utils;

import cn.cool.cherish.Client;
import lombok.experimental.UtilityClass;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.objectweb.asm.ClassReader;
import org.objectweb.asm.ClassWriter;
import org.objectweb.asm.tree.ClassNode;

import static org.objectweb.asm.ClassWriter.COMPUTE_FRAMES;
import static org.objectweb.asm.ClassWriter.COMPUTE_MAXS;

/**
 * ClassHelper
 * <AUTHOR>
 */
@UtilityClass
public class ClassHelper {

    @Nullable
    public ClassNode node(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return null;
        }

        ClassReader reader = new ClassReader(bytes);
        ClassNode node = new ClassNode();
        reader.accept(node, 0);
        return node;
    }

    public byte[] rewriteClass(@NotNull ClassNode node) {
        ClassWriter writer = new ClassWriter(COMPUTE_MAXS | COMPUTE_FRAMES) {
            @Override
            protected @NotNull String getCommonSuperClass(@NotNull String type1, @NotNull String type2) {
                return findCommonSuperClass(type1, type2);
            }
        };
        node.accept(writer);
        return writer.toByteArray();
    }

    @Nullable
    public Class<?> getClass(String name) {
        try {
            return Class.forName(name.replace('/', '.'));
        } catch (Throwable ignored) {
            return null;
        }
    }

    private String findCommonSuperClass(String type1, String type2) {
        try {
            Class<?> class1 = getClass(type1);
            Class<?> class2 = getClass(type2);

            if (class1 == null || class2 == null) {
                return "java/lang/Object";
            }

            if (class1.isAssignableFrom(class2)) {
                return type1;
            }
            if (class2.isAssignableFrom(class1)) {
                return type2;
            }

            if (!class1.isInterface() && !class2.isInterface()) {
                return findCommonSuperClassForClasses(class1, class2);
            }

        } catch (Throwable e) {
            Client.logger.error("Error finding common superclass for {} and {}: {}", type1, type2, e.getMessage());
        }

        return "java/lang/Object";
    }

    private String findCommonSuperClassForClasses(Class<?> class1, Class<?> class2) {
        Class<?> current = class1;
        while (current != null && !current.isAssignableFrom(class2)) {
            current = current.getSuperclass();
        }
        return current != null ? current.getName().replace('.', '/') : "java/lang/Object";
    }
}
