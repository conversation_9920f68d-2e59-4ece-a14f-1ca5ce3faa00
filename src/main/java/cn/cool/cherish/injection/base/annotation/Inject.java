package cn.cool.cherish.injection.base.annotation;

import cn.cool.cherish.injection.base.utils.InsertPosition;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface Inject {
    String method();
    String desc();
    InsertPosition at() default InsertPosition.HEAD;
}
