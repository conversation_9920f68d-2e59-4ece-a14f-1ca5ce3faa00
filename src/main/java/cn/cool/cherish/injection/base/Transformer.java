package cn.cool.cherish.injection.base;

import cn.cool.cherish.Client;
import cn.cool.cherish.injection.base.annotation.ASM;
import cn.cool.cherish.injection.base.annotation.Inject;
import cn.cool.cherish.injection.base.annotation.Mixin;
import cn.cool.cherish.injection.base.annotation.Overwrite;
import cn.cool.cherish.injection.base.impl.ASMHandler;
import cn.cool.cherish.injection.base.impl.InjectHandler;
import cn.cool.cherish.injection.base.impl.OverwriteHandler;
import cn.cool.cherish.injection.base.utils.ClassHelper;
import cn.cool.cherish.utils.NativeUtils;
import lombok.Getter;
import org.objectweb.asm.tree.*;

import java.lang.reflect.Method;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * Transformer
 * <AUTHOR>
 */
@Getter
public class Transformer {
    private final ArrayList<Object> transformers = new ArrayList<>();
    private static final boolean devMode = true; // Output ModifiedClass

    public void addTransformer(Class<?> transformerClass) {
        transformers.add(transformerClass);
    }

    public Map<String, byte[]> transform() {
        Map<String, byte[]> classMap = new HashMap<>();

        for (Object transformer : transformers) {
            Class<?> transformerClass = (Class<?>) transformer;
            Class<?> targetClass = transformerClass.getAnnotation(Mixin.class).value();

            ClassNode targetNode = ClassHelper.node(getClassBytes(targetClass));
            processTransformerMethods(transformerClass, targetNode, targetClass);

            if (targetNode != null) {
                byte[] modifiedBytes = ClassHelper.rewriteClass(targetNode);
                classMap.put(targetClass.getName().replace('/', '.'), modifiedBytes);

                if (devMode) {
                    outputModifiedClass(targetClass.getSimpleName(), modifiedBytes);
                }
            }
        }

        return classMap;
    }

    private void outputModifiedClass(String className, byte[] classBytes) {
        try {
            Path outputDir = Paths.get("modified_classes");
            if (!Files.exists(outputDir)) {
                Files.createDirectories(outputDir);
            }

            Path classFile = outputDir.resolve(className + "_modified.class");
            Files.write(classFile, classBytes);

            System.out.println("Modified class written to: " + classFile.toAbsolutePath());

        } catch (Exception e) {
            System.err.println("Failed to write modified class file for " + className + ": " + e.getMessage());
        }
    }

    private byte[] getClassBytes(Class<?> targetClass) {
        while (true) {
            try {
                return NativeUtils.Z2V0Q2xhc3Nlc0J5dGVz(targetClass);
            } catch (Throwable e) {
                Client.logger.error("Error getting bytes for {}", targetClass.getName(), e);
            }
        }
    }

    private void processTransformerMethods(Class<?> transformerClass, ClassNode targetNode, Class<?> targetClass) {
        for (Method method : transformerClass.getDeclaredMethods()) {
            if (method.isAnnotationPresent(Inject.class)) {
                InjectHandler.handleInject(method, targetNode, targetClass, transformerClass);
            } else if (method.isAnnotationPresent(Overwrite.class)) {
                OverwriteHandler.handleOverwrite(method, targetNode, targetClass, transformerClass);
            } else if (method.isAnnotationPresent(ASM.class)) {
                ASMHandler.handleASM(method, targetNode, targetClass, transformerClass);
            }
        }
    }
}
