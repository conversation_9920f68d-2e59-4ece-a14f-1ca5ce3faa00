package cn.cool.cherish.injection.mixin.player;

import cn.cool.cherish.injection.base.annotation.ASM;
import cn.cool.cherish.injection.base.annotation.Mixin;
import lombok.Getter;
import lombok.Setter;
import net.minecraft.client.Timer;
import org.objectweb.asm.Opcodes;
import org.objectweb.asm.Type;
import org.objectweb.asm.tree.*;

@Mixin(Timer.class)
public class MixinTimer {

    @Setter
    @Getter
    private static float timerSpeed = 1.0F;

    @ASM(method = "advanceTime", desc = "(J)I")
    public void advanceTime(MethodNode method) {
        AbstractInsnNode targetNode = null;

        for (AbstractInsnNode cnm : method.instructions.toArray()) {
            if (cnm.getOpcode() == Opcodes.FDIV) {
                targetNode = cnm;
                break;
            }
        }

        if (targetNode != null) {
            InsnList insnList = new InsnList();

            insnList.add(new MethodInsnNode(
                    Opcodes.INVOKESTATIC,
                    Type.getInternalName(MixinTimer.class),
                    "getTimerSpeed",
                    "()F"
            ));

            insnList.add(new InsnNode(Opcodes.FMUL));
            method.instructions.insert(targetNode, insnList);
        }
    }
}
