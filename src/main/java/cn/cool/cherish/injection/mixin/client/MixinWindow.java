package cn.cool.cherish.injection.mixin.client;

import cn.cool.cherish.Client;
import cn.cool.cherish.evnet.EventManager;
import cn.cool.cherish.evnet.impl.rander.WindowSizeEvent;
import cn.cool.cherish.injection.base.annotation.Inject;
import cn.cool.cherish.injection.base.annotation.Mixin;
import cn.cool.cherish.injection.base.utils.InsertPosition;
import cn.cool.cherish.utils.skija.SkiaProcess;
import com.mojang.blaze3d.platform.Window;

@Mixin(Window.class)
public class MixinWindow {

    @Inject(method = "onResize",desc = "(JII)V", at = InsertPosition.TAIL)
    public static void onResize() {
        if(Client.instance.skiaProcess != null) {
            Client.instance.skiaProcess.initSkia();
            Client.logger.info("Skija Init Success!");
        }

        EventManager.call(new WindowSizeEvent());
    }
}
