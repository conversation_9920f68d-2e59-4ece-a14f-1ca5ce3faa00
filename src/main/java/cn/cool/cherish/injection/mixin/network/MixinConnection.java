package cn.cool.cherish.injection.mixin.network;

import cn.cool.cherish.Client;
import cn.cool.cherish.evnet.EventManager;
import cn.cool.cherish.evnet.base.events.Event;
import cn.cool.cherish.evnet.impl.packet.PacketEvent;
import cn.cool.cherish.injection.base.annotation.ASM;
import cn.cool.cherish.injection.base.annotation.Mixin;
import cn.cool.cherish.utils.wrapper.IMinecraft;
import net.minecraft.network.Connection;
import net.minecraft.network.protocol.Packet;
import org.objectweb.asm.Opcodes;
import org.objectweb.asm.Type;
import org.objectweb.asm.tree.*;

@Mixin(Connection.class)
public class MixinConnection implements IMinecraft {

    @ASM(method = "channelRead0", desc = "(Lio/netty/channel/ChannelHandlerContext;Lnet/minecraft/network/protocol/Packet;)V")
    private void channelRead0(MethodNode node) {
        InsnList instructions = new InsnList();

        instructions.add(new VarInsnNode(Opcodes.ALOAD, 2));
        instructions.add(new MethodInsnNode(
                Opcodes.INVOKESTATIC,
                Type.getInternalName(MixinConnection.class),
                "onPacketReceive",
                "(Lnet/minecraft/network/protocol/Packet;)Z",
                false
        ));

        LabelNode continueLabel = new LabelNode();
        instructions.add(new JumpInsnNode(Opcodes.IFEQ, continueLabel));
        instructions.add(new InsnNode(Opcodes.RETURN));
        instructions.add(continueLabel);

        node.instructions.insert(instructions);
    }

    public static boolean onPacketReceive(Packet<?> packet) {
        if (mc.level == null || mc.player == null || packet == null) return false;

        PacketEvent event = new PacketEvent(Event.Side.RECEIVE, packet);
        EventManager.call(event);

        return event.isCancelled();
    }

    public static boolean onPacketSend(Packet<?> packet) {
        if (mc.level == null || mc.player == null || packet == null) return false;

        PacketEvent event = new PacketEvent(Event.Side.SEND, packet);
        EventManager.call(event);

        return event.isCancelled();
    }

    @ASM(method = "sendPacket", desc = "(Lnet/minecraft/network/protocol/Packet;Lnet/minecraft/network/PacketSendListener;)V")
    private void sendPacket(MethodNode node) {
        InsnList instructions = new InsnList();

        instructions.add(new VarInsnNode(Opcodes.ALOAD, 1));
        instructions.add(new MethodInsnNode(
                Opcodes.INVOKESTATIC,
                Type.getInternalName(MixinConnection.class),
                "onPacketSend",
                "(Lnet/minecraft/network/protocol/Packet;)Z",
                false
        ));

        LabelNode continueLabel = new LabelNode();
        instructions.add(new JumpInsnNode(Opcodes.IFEQ, continueLabel));
        instructions.add(new InsnNode(Opcodes.RETURN));
        instructions.add(continueLabel);

        node.instructions.insert(instructions);
    }
}
