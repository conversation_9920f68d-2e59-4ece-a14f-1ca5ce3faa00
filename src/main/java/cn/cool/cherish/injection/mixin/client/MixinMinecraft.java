package cn.cool.cherish.injection.mixin.client;

import cn.cool.cherish.Client;
import cn.cool.cherish.evnet.EventManager;
import cn.cool.cherish.evnet.impl.player.TickEvent;
import cn.cool.cherish.injection.base.annotation.Inject;
import cn.cool.cherish.injection.base.utils.InsertPosition;
import cn.cool.cherish.injection.base.annotation.Mixin;
import cn.cool.cherish.injection.base.annotation.Overwrite;
import net.minecraft.client.Minecraft;
import org.objectweb.asm.tree.MethodNode;

@Mixin(Minecraft.class)
public class MixinMinecraft {

    @Inject(method = "tick", desc = "()V", at = InsertPosition.HEAD)
    public static void tick() {
        EventManager.call(new TickEvent());
    }

    @Overwrite(method = "getFps", desc = "()I")
    public static int getFps() {
        return 114514;
    }
}
