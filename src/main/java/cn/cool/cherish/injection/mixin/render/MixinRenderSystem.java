package cn.cool.cherish.injection.mixin.render;

import cn.cool.cherish.Client;
import cn.cool.cherish.evnet.EventManager;
import cn.cool.cherish.evnet.impl.rander.EventRenderSkija;
import cn.cool.cherish.injection.base.annotation.ASM;
import cn.cool.cherish.injection.base.annotation.Mixin;
import com.mojang.blaze3d.systems.RenderSystem;
import org.objectweb.asm.Opcodes;
import org.objectweb.asm.Type;
import org.objectweb.asm.tree.*;

@Mixin(RenderSystem.class)
public class MixinRenderSystem {

    @ASM(method = "flipFrame", desc = "(J)V")
    public void transformFlipFrame(MethodNode methodNode) {
        AbstractInsnNode targetNode = null;
        for (AbstractInsnNode insn : methodNode.instructions) {
            if (insn instanceof MethodInsnNode methodInsn) {
                if ("glfwSwapBuffers".equals(methodInsn.name) && "(J)V".equals(methodInsn.desc)) {
                    targetNode = insn;
                    break;
                }
            }
        }

        if (targetNode != null) {
            InsnList toInsert = new InsnList();

            toInsert.add(new MethodInsnNode(
                    Opcodes.INVOKESTATIC,
                    Type.getInternalName(MixinRenderSystem.class),
                    "onFlipFrame",
                    "()V",
                    false
            ));

            methodNode.instructions.insertBefore(targetNode, toInsert);
        }
    }

    public static void onFlipFrame() {
        EventManager.call(new EventRenderSkija());
    }
}
