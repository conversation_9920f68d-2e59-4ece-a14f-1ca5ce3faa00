package cn.cool.cherish.injection.mixin.client;

import cn.cool.cherish.Client;
import cn.cool.cherish.evnet.EventManager;
import cn.cool.cherish.evnet.impl.input.KeyEvent;
import cn.cool.cherish.injection.base.annotation.ASM;
import cn.cool.cherish.injection.base.annotation.Mixin;
import net.minecraft.client.KeyboardHandler;
import org.lwjgl.glfw.GLFW;
import org.objectweb.asm.Opcodes;
import org.objectweb.asm.Type;
import org.objectweb.asm.tree.InsnList;
import org.objectweb.asm.tree.MethodInsnNode;
import org.objectweb.asm.tree.MethodNode;
import org.objectweb.asm.tree.VarInsnNode;

@Mixin(KeyboardHandler.class)
public class MixinKeyboard {

    @ASM(method = "keyPress", desc = "(JIIII)V")
    public void keyPress(MethodNode methodNode) {
        InsnList list = new InsnList();

        list.add(new VarInsnNode(Opcodes.ILOAD, 3));
        list.add(new VarInsnNode(Opcodes.ILOAD, 5));
        list.add(new MethodInsnNode(Opcodes.INVOKESTATIC, Type.getInternalName(MixinKeyboard.class), "onKey", "(II)V"));

        methodNode.instructions.insert(list);
    }

    public static void onKey(int key, int action) {
        if (action == GLFW.GLFW_PRESS) {
            if (Client.instance != null) {
                EventManager.call(new KeyEvent(key));
            }
        }
    }
}
