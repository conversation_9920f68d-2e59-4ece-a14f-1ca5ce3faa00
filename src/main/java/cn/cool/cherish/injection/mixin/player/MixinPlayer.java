package cn.cool.cherish.injection.mixin.player;

import cn.cool.cherish.Client;
import cn.cool.cherish.injection.base.annotation.ASM;
import cn.cool.cherish.injection.base.annotation.Mixin;
import cn.cool.cherish.injection.base.utils.mapping.Mapping;
import cn.cool.cherish.module.impl.movement.KeepSprint;
import cn.cool.cherish.module.impl.player.NoSlowBreak;
import cn.cool.cherish.utils.wrapper.IMinecraft;
import net.minecraft.tags.TagKey;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.material.Fluid;
import net.minecraft.world.phys.Vec3;
import org.objectweb.asm.Opcodes;
import org.objectweb.asm.Type;
import org.objectweb.asm.tree.*;

import java.util.Objects;

import static org.objectweb.asm.Opcodes.*;

@Mixin(Player.class)
public class MixinPlayer implements IMinecraft {
    private static final String SET_SPRINTING_METHOD = Mapping.get(Entity.class, "setSprinting", "(Z)V");
    private static final String SET_DELTA_MOVEMENT_METHOD = Mapping.get(Entity.class, "setDeltaMovement", "(Lnet/minecraft/world/phys/Vec3;)V");
    private static final String VEC3_MULTIPLY_METHOD = Mapping.get(Vec3.class, "multiply", "(DDD)Lnet/minecraft/world/phys/Vec3;");

    public static boolean isKeepSprint() {
        return Client.instance != null && Client.instance.getModuleManager().getModule(KeepSprint.class).isEnabled();
    }

    public static boolean getMiningFatigue(Player player) {
        return Client.instance != null && player == mc.player && NoSlowBreak.instance.isEnabled() && NoSlowBreak.instance.miningFatigue.getValue();
    }

    public static boolean getOnAir(Player player) {
        return Client.instance != null && player == mc.player && NoSlowBreak.instance.isEnabled() && NoSlowBreak.instance.onAir.getValue();
    }

    public static boolean getOnWater(Player player) {
        return Client.instance != null && player == mc.player && NoSlowBreak.instance.isEnabled() && NoSlowBreak.instance.onWater.getValue();
    }

    @ASM(method = "getDigSpeed", desc = "(Lnet/minecraft/world/level/block/state/BlockState;Lnet/minecraft/core/BlockPos;)F")
    public void getDigSpeed(MethodNode methodNode) {
        InsnList instructions = methodNode.instructions;
        AbstractInsnNode[] insnArray = instructions.toArray();

        for (int i = 0; i < insnArray.length; i++) {
            AbstractInsnNode insn = insnArray[i];

            // Effect
            if (insn.getOpcode() == INVOKEVIRTUAL) {
                MethodInsnNode methodInsn = (MethodInsnNode) insn;

                if (methodInsn.name.equals(Mapping.get(LivingEntity.class, "hasEffect", "(Lnet/minecraft/world/effect/MobEffect;)Z")) &&
                        methodInsn.desc.equals("(Lnet/minecraft/world/effect/MobEffect;)Z") &&
                        isDIGSlowdownCheck(insnArray, i)) {

                    methodInsn.owner = Type.getInternalName(MixinPlayer.class);
                    methodInsn.name = "hasEffectWrapper";
                    methodInsn.desc = "(Lnet/minecraft/world/entity/player/Player;Lnet/minecraft/world/effect/MobEffect;)Z";
                    methodInsn.setOpcode(INVOKESTATIC);
                }
            }

            // Fluid
            if (insn.getOpcode() == INVOKEVIRTUAL) {
                MethodInsnNode methodInsn = (MethodInsnNode) insn;

                if (methodInsn.name.equals(Mapping.get(Entity.class, "isEyeInFluid", "(Lnet/minecraft/tags/TagKey;)Z")) && methodInsn.desc.equals("(Lnet/minecraft/tags/TagKey;)Z")) {
                    methodInsn.owner = Type.getInternalName(MixinPlayer.class);
                    methodInsn.name = "isEyeInFluidWrapper";
                    methodInsn.desc = "(Lnet/minecraft/world/entity/player/Player;Lnet/minecraft/tags/TagKey;)Z";
                    methodInsn.setOpcode(INVOKESTATIC);
                }
            }

            // Ground
            if (insn.getOpcode() == INVOKEVIRTUAL) {
                MethodInsnNode methodInsn = (MethodInsnNode) insn;

                if (methodInsn.name.equals(Mapping.get(Entity.class, "onGround", "()Z")) && methodInsn.desc.equals("()Z")) {
                    methodInsn.owner = Type.getInternalName(MixinPlayer.class);
                    methodInsn.name = "onGroundWrapper";
                    methodInsn.desc = "(Lnet/minecraft/world/entity/player/Player;)Z";
                    methodInsn.setOpcode(INVOKESTATIC);
                }
            }
        }
    }

    public static boolean hasEffectWrapper(Player player, MobEffect effect) {
        if (getMiningFatigue(player)) {
            return false;
        }
        return player.hasEffect(effect);
    }

    public static boolean isEyeInFluidWrapper(Player player, TagKey<Fluid> fluidTag) {
        if (getOnWater(player)) {
            return false;
        }
        return player.isEyeInFluid(fluidTag);
    }

    public static boolean onGroundWrapper(Player player) {
        if (getOnAir(player)) {
            return true;
        }
        return player.onGround();
    }

    private boolean isDIGSlowdownCheck(AbstractInsnNode[] insnArray, int index) {
        for (int i = Math.max(0, index - 5); i < index; i++) {
            if (insnArray[i].getOpcode() == GETSTATIC) {
                FieldInsnNode fieldInsn = (FieldInsnNode) insnArray[i];
                if (fieldInsn.name.contains(Objects.requireNonNull(Mapping.get(MobEffects.class, "DIG_SLOWDOWN", null)))) {
                    return true;
                }
            }
        }
        return false;
    }

    @ASM(method = "attack", desc = "(Lnet/minecraft/world/entity/Entity;)V")
    public void attack(MethodNode methodNode) {
        InsnList instructions = methodNode.instructions;
        AbstractInsnNode[] insnArray = instructions.toArray();

        for (int i = 0; i < insnArray.length; i++) {
            AbstractInsnNode insn = insnArray[i];

            if (insn.getOpcode() == Opcodes.INVOKEVIRTUAL) {
                MethodInsnNode methodInsn = (MethodInsnNode) insn;

                if (methodInsn.name.equals(SET_SPRINTING_METHOD) && methodInsn.desc.equals("(Z)V")) {

                    if (i > 0 && insnArray[i-1].getOpcode() == Opcodes.ICONST_0) {
                        patchSetSprintingCall(instructions, methodInsn);
                    }
                } else if (methodInsn.name.equals(SET_DELTA_MOVEMENT_METHOD) && methodInsn.desc.equals("(Lnet/minecraft/world/phys/Vec3;)V")) {
                    if (isKnockbackRelatedCall(insnArray, i)) {
                        patchSetDeltaMovementCall(instructions, methodInsn);
                    }
                }
            }
        }
    }

    private boolean isKnockbackRelatedCall(AbstractInsnNode[] insnArray, int currentIndex) {
        for (int i = Math.max(0, currentIndex - 15); i < currentIndex; i++) {
            AbstractInsnNode insn = insnArray[i];
            if (insn.getOpcode() == Opcodes.INVOKEVIRTUAL) {
                MethodInsnNode methodInsn = (MethodInsnNode) insn;
                if (methodInsn.name.equals(VEC3_MULTIPLY_METHOD)) {
                    for (int j = Math.max(0, i - 10); j < i; j++) {
                        if (insnArray[j].getOpcode() == Opcodes.LDC) {
                            LdcInsnNode ldcInsn = (LdcInsnNode) insnArray[j];
                            if (ldcInsn.cst instanceof Double && Math.abs((Double)ldcInsn.cst - 0.6) < 0.001) {
                                return true;
                            }
                        }
                    }
                }
            }
        }
        return false;
    }

    private void patchSetSprintingCall(InsnList instructions, MethodInsnNode setSprintingCall) {
        InsnList patch = new InsnList();

        patch.add(new MethodInsnNode(
                INVOKESTATIC,
                Type.getInternalName(MixinPlayer.class),
                "isKeepSprint",
                "()Z",
                false
        ));

        LabelNode skipCall = new LabelNode();
        patch.add(new JumpInsnNode(Opcodes.IFNE, skipCall));

        instructions.insertBefore(setSprintingCall, patch);

        InsnList afterCall = new InsnList();
        LabelNode continueLabel = new LabelNode();
        afterCall.add(new JumpInsnNode(Opcodes.GOTO, continueLabel));

        afterCall.add(skipCall);
        afterCall.add(new InsnNode(Opcodes.POP));
        afterCall.add(new InsnNode(Opcodes.POP));
        afterCall.add(continueLabel);

        instructions.insert(setSprintingCall, afterCall);
    }

    private void patchSetDeltaMovementCall(InsnList instructions, MethodInsnNode setDeltaMovementCall) {
        InsnList patch = new InsnList();

        patch.add(new MethodInsnNode(
                INVOKESTATIC,
                Type.getInternalName(MixinPlayer.class),
                "isKeepSprint",
                "()Z",
                false
        ));

        LabelNode skipCall = new LabelNode();
        patch.add(new JumpInsnNode(Opcodes.IFNE, skipCall));

        instructions.insertBefore(setDeltaMovementCall, patch);

        InsnList afterCall = new InsnList();
        LabelNode continueLabel = new LabelNode();
        afterCall.add(new JumpInsnNode(Opcodes.GOTO, continueLabel));

        afterCall.add(skipCall);
        afterCall.add(new InsnNode(Opcodes.POP));
        afterCall.add(new InsnNode(Opcodes.POP));
        afterCall.add(continueLabel);

        instructions.insert(setDeltaMovementCall, afterCall);
    }
}