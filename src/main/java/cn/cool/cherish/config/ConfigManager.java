package cn.cool.cherish.config;

import cn.cool.cherish.Client;
import cn.cool.cherish.config.impl.ModuleConfig;
import cn.cool.cherish.utils.wrapper.IMinecraft;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonElement;
import com.google.gson.JsonParser;
import lombok.Setter;

import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;

@Setter
public class ConfigManager implements IMinecraft {
    public static final List<Config> configs = new ArrayList<>();
    public static final File dir = new File(System.getProperty("user.home"), ".cool" + File.separator + "Cherish");
    private static final Gson gson = new GsonBuilder().setPrettyPrinting().create();

    public ConfigManager() {
        try {
            Files.createDirectories(dir.toPath());
        } catch (IOException e) {
            System.err.println("Failed to create directory: " + e.getMessage());
        }

        configs.add(new ModuleConfig("modules.json"));
        configs.forEach(config -> loadConfig(config.getName()));
    }

    public void loadConfig(String name) {
        File file = new File(dir, name);

        if (!file.exists()) {
            saveConfig(name);
            return;
        }

        Config targetConfig = findConfigByName(name);
        if (targetConfig == null) {
            return;
        }

        try (FileReader reader = new FileReader(file)) {
            JsonElement jsonElement = JsonParser.parseReader(reader);
            targetConfig.loadConfig(jsonElement.getAsJsonObject());
        } catch (IOException e) {
            saveConfig(name);
        }
    }

    public void loadUserConfig(String name) {
        File file = new File(dir, name);

        if (!file.exists()) {
            saveUserConfig(name);
            return;
        }

        Config moduleConfig = findConfigByName("modules.json");
        if (moduleConfig == null) {
            return;
        }

        try (FileReader reader = new FileReader(file)) {
            JsonElement jsonElement = JsonParser.parseReader(reader);
            moduleConfig.loadConfig(jsonElement.getAsJsonObject());
        } catch (IOException e) {
            saveUserConfig(name);
        }
    }

    public void saveConfig(String name) {
        File file = new File(dir, name);
        Config targetConfig = findConfigByName(name);
        if (targetConfig == null) return;

        try {
            Files.createDirectories(file.getParentFile().toPath());
            String jsonContent = gson.toJson(targetConfig.saveConfig());
            Files.writeString(file.toPath(), jsonContent);
        } catch (IOException ignored) {}
    }

    public void saveUserConfig(String name) {
        File file = new File(dir, name);
        Config moduleConfig = findConfigByName("modules.json");
        if (moduleConfig == null) return;

        try {
            Files.createDirectories(file.getParentFile().toPath());
            String jsonContent = gson.toJson(moduleConfig.saveConfig());
            Files.writeString(file.toPath(), jsonContent);
        } catch (IOException ignored) {}
    }

    private Config findConfigByName(String name) {
        return configs.stream().filter(config -> config.getName().equals(name)).findFirst().orElse(null);
    }

    public void saveAllConfig() {
        Client.logger.info("Saving all configs...");
        configs.forEach(config -> saveConfig(config.getName()));
    }
}
