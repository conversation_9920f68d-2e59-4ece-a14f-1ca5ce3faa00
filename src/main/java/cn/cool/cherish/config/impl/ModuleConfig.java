package cn.cool.cherish.config.impl;

import cn.cool.cherish.Client;
import cn.cool.cherish.config.Config;
import cn.cool.cherish.module.Module;
import cn.cool.cherish.module.value.Value;
import cn.cool.cherish.module.value.impl.*;
import cn.cool.cherish.utils.render.ColorUtil;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

import java.awt.*;

public class ModuleConfig extends Config {
    public ModuleConfig(String name) {
        super(name);
    }

    @Override
    public void loadConfig(JsonObject object) {
        for (Module module : Client.instance.getModuleManager().getModules()) {
            if (object.has(module.getName())) {

                JsonObject moduleObject = object.get(module.getName()).getAsJsonObject();

                if (moduleObject.has("State")) {
                    module.setEnabled(moduleObject.get("State").getAsBoolean());
                }

                if (moduleObject.has("Key")) {
                    module.setKeyBind(moduleObject.get("Key").getAsInt());
                }
                if (moduleObject.has("Hidden")) {
                    module.setHidden(moduleObject.get("Hidden").getAsBoolean());
                }

                if (moduleObject.has("Values")) {
                    JsonObject valuesObject = moduleObject.get("Values").getAsJsonObject();

                    for (Value value : module.getValues()) {
                        if (valuesObject.has(value.getName())) {
                            JsonElement theValue = valuesObject.get(value.getName());

                            if (value instanceof NumberValue numberValue) {
                                numberValue.setValue(theValue.getAsNumber().floatValue());
                            }

                            if (value instanceof BoolValue boolValue) {
                                boolValue.setValue(theValue.getAsBoolean());
                            }

                            if (value instanceof ModeValue modeValue) {
                                modeValue.setValue(theValue.getAsString());
                            }

                            if (value instanceof MultiBoolValue multiBoolValue) {
                                if (theValue.getAsString().isEmpty()) {
                                    multiBoolValue.getToggled().forEach(option -> option.setValue(false));
                                }
                                if (!theValue.getAsString().isEmpty()) {
                                    String[] strings = theValue.getAsString().split(", ");
                                    multiBoolValue.getToggled().forEach(option -> option.setValue(false));
                                    for (String string : strings) {
                                        multiBoolValue.getValues().stream().filter(setting -> setting.getName().equalsIgnoreCase(string)).forEach(boolValue -> boolValue.setValue(true));
                                    }
                                }
                            }

                            if (value instanceof ColorValue colorValue) {
                                JsonObject colorValues = theValue.getAsJsonObject();
                                colorValue.setValue(ColorUtil.applyOpacity(new Color(colorValues.get("RGB").getAsInt()), colorValues.get("Alpha").getAsFloat()));
                            }

                            if (value instanceof TextValue textValue) {
                                textValue.setText(theValue.getAsString());
                            }
                        }
                    }
                }
            }
        }
    }

    @Override
    public JsonObject saveConfig() {
        JsonObject object = new JsonObject();
        for (Module module : Client.instance.getModuleManager().getModules()) {
            JsonObject moduleObject = new JsonObject();

            moduleObject.addProperty("State", module.isEnabled());
            moduleObject.addProperty("Key", module.getKeyBind());
            moduleObject.addProperty("Hidden", module.isHidden());

            JsonObject valuesObject = new JsonObject();

            for (Value value : module.getValues()) {
                if (value instanceof NumberValue numberValue) {
                    valuesObject.addProperty(value.getName(), numberValue.getValue());
                }
                if (value instanceof BoolValue boolValue) {
                    valuesObject.addProperty(value.getName(), boolValue.getValue());
                }
                if (value instanceof ModeValue modeValue) {
                    valuesObject.addProperty(value.getName(), modeValue.getValue());
                }
                if (value instanceof MultiBoolValue multiBoolValue) {
                    valuesObject.addProperty(value.getName(), multiBoolValue.isEnabled());
                }
                if (value instanceof ColorValue colorValue) {
                    JsonObject colorValues = new JsonObject();
                    colorValues.addProperty("RGB", Color.HSBtoRGB(colorValue.getHue(), colorValue.getSaturation(), colorValue.getBrightness()));
                    colorValues.addProperty("Alpha", colorValue.getAlpha());
                    valuesObject.add(colorValue.getName(), colorValues);
                }
                if (value instanceof TextValue textValue) {
                    valuesObject.addProperty(value.getName(), textValue.getText());
                }
            }

            moduleObject.add("Values", valuesObject);
            object.add(module.getName(), moduleObject);
        }
        return object;
    }
}