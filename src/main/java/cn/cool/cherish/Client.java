package cn.cool.cherish;

import cn.cool.cherish.command.CommandManager;
import cn.cool.cherish.config.ConfigManager;
import cn.cool.cherish.evnet.EventManager;
import cn.cool.cherish.injection.MixinLoader;
import cn.cool.cherish.module.ModuleManager;
import cn.cool.cherish.utils.skija.SkiaProcess;
import cn.cool.cherish.utils.skija.SkiaUtils;
import cn.cool.cherish.utils.skija.font.SkiaFontManager;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.logging.LogUtils;
import lombok.Getter;
import net.minecraftforge.fml.common.Mod;
import org.slf4j.Logger;

import java.io.File;
import java.util.concurrent.CountDownLatch;

@Getter
@Mod("cherish")
public final class Client {
    public static Logger logger;
    public static Client instance;

    public final String name = "Cherish";
    public final String version = "B4";
    public final File mainFile;

    public CommandManager commandManager;
    public ConfigManager configManager;
    public ModuleManager moduleManager;
    public SkiaProcess skiaProcess;

    public Client() {
        logger  = LogUtils.getLogger();
        instance = this;
        mainFile = new File(System.getProperty("user.home"), ".cool\\" + name);


        CountDownLatch latch = new CountDownLatch(1);
        RenderSystem.recordRenderCall(() -> {
            System.setProperty("skija.library.path", mainFile + "\\resources\\dll");
            skiaProcess = new SkiaProcess();
            System.out.println("Skija Init!!!!!!!!!!!!!!!!");
            latch.countDown();
        });

        MixinLoader.init();

        init();
    }

    public void init() {
        moduleManager = new ModuleManager();
        configManager = new ConfigManager();
        commandManager = new CommandManager();

        logger.info("Welcome to Cherish Client!");
    }


    public void shutdown() {
        if (Client.instance != null) {
            configManager.saveAllConfig();
            logger.info("Stopping " + name + " Client");
        }
    }
}
