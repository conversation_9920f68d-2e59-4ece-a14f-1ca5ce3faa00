package cn.cool.cherish.utils.file;

import cn.cool.cherish.utils.wrapper.IMinecraft;
import com.mojang.blaze3d.platform.NativeImage;
import net.minecraft.client.renderer.texture.DynamicTexture;
import net.minecraft.resources.ResourceLocation;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

public class LocalResource implements IMinecraft {
    public final static Map<String, byte[]> resources = new HashMap<>();
//    public static void loadTexture(String name, InputStream inputStream) throws IOException {
//
//        NativeImage a = NativeImage.read(inputStream);
//        ResourceLocation AD = new ResourceLocation("/" + name);
//        mc.textureManager.register(AD, new DynamicTexture(a));
//        resources.put(name, inputStream.readAllBytes());
//    }
//    public static void loadTexture(ArrayList<String> files, String path) throws IOException {
//        for (String str : files) {
//            int i = 0;
//            boolean check = false;
//            while(i < str.length()) {
//                if (Character.isUpperCase(str.charAt(i))) {
//                    check = true;
//                    break;
//                }
//                ++i;
//            }
//            if(check) continue;
//            Path path1 = Paths.get(path + "\\" + str);
//            NativeImage a = NativeImage.read(Files.newInputStream(path1));
//            ResourceLocation AD = new ResourceLocation("/" + str);
//            mc.textureManager.register(AD, new DynamicTexture(a));
//            byte[] data = Files.readAllBytes(path1);
//            resources.put(str, data);
//        }
//    }
}
