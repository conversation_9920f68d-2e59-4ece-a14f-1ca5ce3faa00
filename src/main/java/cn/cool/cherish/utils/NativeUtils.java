package cn.cool.cherish.utils;

import lombok.experimental.UtilityClass;
import org.objectweb.asm.tree.MethodNode;

@UtilityClass
public class NativeUtils {
    static {
        System.load("C:\\native_utils.dll");
    }

    public static native void cmVkZWZpbmVDbGFzc2Vz(ClassLoader classLoader, Class<?> targetClass, byte[] newClassBytes, boolean printError, boolean printSuccessful);// redefineClasses
    public static native byte[] Z2V0Q2xhc3Nlc0J5dGVz(Class<?> clazz);// getClassesBytes
    public static native void RnVja3dvY25tc2J4anBoYXNob29vc2V0(MethodNode methodNode, String[] stringArray);// HashSet
    public static native void RnVja3dvY25tc2J4anBoYXNob29vbw(MethodNode methodNode, String[] stringArray);// HashMap
    public static native String UFVCTElDS0VZ();// PUBLICKEY
    public static native String UFJJVkFURUtFWQ();// PRIVATEKEY

    public static void cmVkZWZpbmVDbGFzc2VzTm9TdWNjZXNzZnVsUHJpbnQ(Class<?> targetClass, byte[] newClassBytes) {
        cmVkZWZpbmVDbGFzc2Vz(targetClass.getClassLoader(), targetClass, newClassBytes, true, false);
    }

    public static void cmVkZWZpbmVDbGFzc2Vz(Class<?> targetClass, byte[] newClassBytes) {
        cmVkZWZpbmVDbGFzc2Vz(targetClass.getClassLoader(), targetClass, newClassBytes, true, true);
    }
}