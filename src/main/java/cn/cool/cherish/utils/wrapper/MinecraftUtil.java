package cn.cool.cherish.utils.wrapper;

import cn.cool.cherish.Client;
import net.minecraft.client.Minecraft;
import net.minecraftforge.fml.ModList;
import net.minecraftforge.forgespi.language.IModFileInfo;
import net.minecraftforge.forgespi.language.IModInfo;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

public class MinecraftUtil implements IMinecraft {
    public static Minecraft getMinecraft() {
        Minecraft minecraft = null;

        try {
            Class<?> classMinecraft = Class.forName("net.minecraft.client.Minecraft");
            for (Field field : classMinecraft.getDeclaredFields()) {
                if (field.getType() == classMinecraft) {
                    field.setAccessible(true);
                    minecraft = (Minecraft) field.get(null);
                    field.setAccessible(false);
                }
            }
        } catch (Throwable e) {
            Client.logger.error("Failed to get Minecraft instance", e);
        }
        return minecraft;
    }

    public static void removeModInfo() {
        ModList.get().getMods().removeIf(modInfo -> modInfo.getModId().equals("cherish"));

        try {
            List<IModFileInfo> modeInfo = new ArrayList<>();
            for (IModFileInfo fileInfo : ModList.get().getModFiles()) {
                for (IModInfo modInfo : fileInfo.getMods()) {
                    if (modInfo.getModId().equals("cherish")) {
                        modeInfo.add(fileInfo);
                    }
                }
            }
            ModList.get().getModFiles().removeAll(modeInfo);
        } catch (Throwable e) {
            Client.logger.error("Failed to remove ModeInfo", e);
        }
    }
}
