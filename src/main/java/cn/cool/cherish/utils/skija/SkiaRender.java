package cn.cool.cherish.utils.skija;

import cn.cool.cherish.utils.file.LocalResource;
import cn.cool.cherish.utils.skija.fbo.FrameBuffers;
import cn.cool.cherish.utils.skija.fbo.GameFrameBuffer;
import cn.cool.cherish.utils.skija.utils.ImageHelper;
import cn.cool.cherish.utils.wrapper.IMinecraft;
import com.mojang.blaze3d.platform.Lighting;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.PoseStack;
import io.github.humbleui.skija.*;
import io.github.humbleui.types.RRect;
import io.github.humbleui.types.Rect;
import net.minecraft.client.multiplayer.PlayerInfo;
import net.minecraft.client.player.AbstractClientPlayer;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


public class SkiaRender implements IMinecraft {

    private static final Map<String, Image> images = new HashMap<>();
    public static final String LOGO_CHAR = "1";
    
    public static void drawRect(CanvasStack canvasStack, float x, float y, float width, float height, int color) {
        Paint redPaint = new Paint().setColor(color);
        x = SkiaUtils.transformCoord(x);
        y = SkiaUtils.transformCoord(y);
        width = SkiaUtils.transformCoord(width);
        height = SkiaUtils.transformCoord(height);

        Rect rect = Rect.makeXYWH(x, y, width, height);
        canvasStack.canvas.drawRect(rect, redPaint);
    }
    public static void scissorRect(CanvasStack canvasStack, float x, float y, float width, float height, ClipMode clipMode) {
        x = SkiaUtils.transformCoord(x);
        y = SkiaUtils.transformCoord(y);
        width = SkiaUtils.transformCoord(width);
        height = SkiaUtils.transformCoord(height);
        canvasStack.canvas.clipRect(
                Rect.makeXYWH(x,y,width,height),
                clipMode
        );
    }
    public static void drawCircle(CanvasStack canvasStack, float x, float y, float radius, int color) {
        if (canvasStack == null || canvasStack.canvas == null) {
            return;
        }

        Paint paint = new Paint();
        paint.setColor(color);
        paint.setAntiAlias(true);

        canvasStack.canvas.drawCircle(x, y, radius, paint);
    }
    public static void scissorRoundedRect(CanvasStack canvasStack, float x, float y, float width, float height,float radius,ClipMode clipMode) {
        x = SkiaUtils.transformCoord(x);
        y = SkiaUtils.transformCoord(y);
        width = SkiaUtils.transformCoord(width);
        height = SkiaUtils.transformCoord(height);
        canvasStack.canvas.clipRRect(
                RRect.makeXYWH(x,y,width,height,radius),
                clipMode
        );
    }

    public static void drawImage(CanvasStack canvasStack, String imageLocal, float x, float y, float width, float height,boolean blend, int color) {
        byte[] bytes = LocalResource.resources.get(imageLocal);
        if(bytes == null) {
            System.out.println(imageLocal + " null");
            return;
        }
        x = SkiaUtils.transformCoord(x);
        y = SkiaUtils.transformCoord(y);
        width = SkiaUtils.transformCoord(width);
        height = SkiaUtils.transformCoord(height);
        Image image = images.get(imageLocal);

        if(image == null) {
            image = Image.makeDeferredFromEncodedBytes(bytes);
            images.put(imageLocal, image);
        }

        Rect dst = Rect.makeXYWH(x, y, width, height);
        Paint paint = new Paint();
        paint.setColor(color);
        if(blend)
            paint.setColorFilter(ColorFilter.makeLighting(0xFFFFFFFF, 0x000000)); // 保留所有颜色，减去黑色
        paint.setAntiAlias(true);
        canvasStack.canvas.drawImageRect(image, Rect.makeXYWH(0, 0, image.getWidth(), image.getHeight()), dst, paint);
    }

    public static void drawImage(CanvasStack canvasStack, String imageLocal, float x, float y, float width, float height) {
        drawImage(canvasStack, imageLocal, x, y, width, height, true, -1);
    }

    public static void drawImage(CanvasStack canvasStack, Image image, float x, float y, float width, float height) {
        x = SkiaUtils.transformCoord(x);
        y = SkiaUtils.transformCoord(y);
        width = SkiaUtils.transformCoord(width);
        height = SkiaUtils.transformCoord(height);
        Rect dst = Rect.makeXYWH(x, y, width, height);
        Paint paint = new Paint();
        paint.setAntiAlias(true);

        canvasStack.canvas.drawImageRect(image, Rect.makeXYWH(0, 0, image.getWidth(), image.getHeight()), dst, paint);
    }

    public static void drawImageIntersect(CanvasStack canvasStack, Image image, float x, float y, float width, float height, float radius) {
        x = SkiaUtils.transformCoord(x);
        y = SkiaUtils.transformCoord(y);
        width = SkiaUtils.transformCoord(width);
        height = SkiaUtils.transformCoord(height);
        canvasStack.push();
        boolean round = radius != 0;
        canvasStack.push();
        if(round)
            canvasStack.canvas.clipRRect(RRect.makeXYWH(x,y,width,height,radius), ClipMode.INTERSECT);
        else
            canvasStack.canvas.clipRect(Rect.makeXYWH(x,y,width,height), ClipMode.INTERSECT);

//        canvasStack.canvas.clipRect(Rect.makeXYWH(x, y, width, height), ClipMode.INTERSECT);
        canvasStack.canvas.drawImage(image, x, y);
        canvasStack.pop();
    }

    public static void drawImageIntersect(CanvasStack canvasStack, String imageLocal, float x, float y, float width, float height, boolean blend, int color) {
        byte[] bytes = LocalResource.resources.get(imageLocal);
        if(bytes == null) {
            System.out.println(imageLocal + " null");
            return;
        }

        Image image = images.get(imageLocal);

        if(image == null) {
            image = Image.makeDeferredFromEncodedBytes(bytes);
            images.put(imageLocal, image);
        }

        Paint paint = new Paint();
        if(blend) {
            paint.setColor(color);
            paint.setColorFilter(ColorFilter.makeLighting(0xFFFFFFFF, 0x000000)); // 保留所有颜色，减去黑色
        }
        paint.setAntiAlias(true);

        x = SkiaUtils.transformCoord(x);
        y = SkiaUtils.transformCoord(y);
        width = SkiaUtils.transformCoord(width);
        height = SkiaUtils.transformCoord(height);

        canvasStack.push();
        canvasStack.canvas.clipRect(Rect.makeXYWH(x, y, width, height), ClipMode.INTERSECT);

        Rect dst = Rect.makeXYWH(x, y, width, height);
        canvasStack.canvas.drawImageRect(image, Rect.makeXYWH(0, 0, image.getWidth(), image.getHeight()), dst, paint);
        canvasStack.pop();
    }
    
    public static void drawPlayerHead(CanvasStack canvasStack, Player player, float x, float y, float width, float height, float cornerRadius) {
        if (player == null || canvasStack.canvas == null) {
            drawErrorHead(canvasStack, x, y, width, height, cornerRadius);
            return;
        }

        try {
            ResourceLocation skinLocation = null;
            try {
                if (player instanceof AbstractClientPlayer) {
                    skinLocation = ((AbstractClientPlayer) player).getSkinTextureLocation();
                }
            } catch (Exception e) {
                System.err.println("获取玩家皮肤位置失败: " + e.getMessage());
            }
            if (skinLocation == null) {
                drawErrorHead(canvasStack, x, y, width, height, cornerRadius);
                return;
            }
            float scaledX = SkiaUtils.transformCoord(x);
            float scaledY = SkiaUtils.transformCoord(y);
            float scaledWidth = SkiaUtils.transformCoord(width);
            float scaledHeight = SkiaUtils.transformCoord(height);
            Image headImage = ImageHelper.extractRegion(skinLocation, 8, 8, 8, 8);
            if (headImage == null || headImage.isClosed()) {
                drawErrorHead(canvasStack, x, y, width, height, cornerRadius);
                return;
            }
            Image hatImage = ImageHelper.extractRegion(skinLocation, 40, 8, 8, 8);
            RRect clipRect = RRect.makeXYWH(scaledX, scaledY, scaledWidth, scaledHeight, cornerRadius);
            Paint paint = new Paint();
            try (paint) {
                paint.setAntiAlias(true);
                canvasStack.push();
                canvasStack.canvas.clipRRect(clipRect, ClipMode.INTERSECT);
                canvasStack.canvas.drawImageRect(
                        headImage,
                        Rect.makeXYWH(0, 0, headImage.getWidth(), headImage.getHeight()),
                        Rect.makeXYWH(scaledX, scaledY, scaledWidth, scaledHeight),
                        paint
                );
                if (hatImage != null && !hatImage.isClosed()) {
                    Paint hatPaint = new Paint();
                    hatPaint.setAntiAlias(true);
                    hatPaint.setBlendMode(BlendMode.SRC_OVER);

                    canvasStack.canvas.drawImageRect(
                            hatImage,
                            Rect.makeXYWH(0, 0, hatImage.getWidth(), hatImage.getHeight()),
                            Rect.makeXYWH(scaledX, scaledY, scaledWidth, scaledHeight),
                            hatPaint
                    );

                    hatPaint.close();
                }
            } finally {
                canvasStack.pop();
            }
        } catch (Exception e) {
            System.err.println("绘制玩家头像失败: " + e.getMessage());
            e.printStackTrace();
            drawErrorHead(canvasStack, x, y, width, height, cornerRadius);
        }
    }


    /**
     * 绘制错误头像，在无法加载正常头像时使用
     */
    private static void drawErrorHead(CanvasStack canvasStack, float x, float y, float width, float height, float radius) {
        if (canvasStack == null || canvasStack.canvas == null) {
            return;
        }

        try {
            x = SkiaUtils.transformCoord(x);
            y = SkiaUtils.transformCoord(y);
            width = SkiaUtils.transformCoord(width);
            height = SkiaUtils.transformCoord(height);
            Paint backgroundPaint = new Paint();
            backgroundPaint.setColor(0xFFFF00FF);
            RRect rrect = RRect.makeXYWH(x, y, width, height, radius);
            canvasStack.canvas.drawRRect(rrect, backgroundPaint);
            Paint gridPaint = new Paint();
            gridPaint.setColor(0xFF000000);
            gridPaint.setStrokeWidth(1.0f);
            gridPaint.setMode(PaintMode.STROKE);
            gridPaint.setAntiAlias(true);
            float gridSize = Math.min(width, height) / 4;
            if (gridSize < 1) gridSize = 1;
            for (int i = 0; i <= 4; i++) {
                float xPos = x + i * gridSize;
                float yPos = y + i * gridSize;
                if (xPos <= x + width) {
                    canvasStack.canvas.drawLine(xPos, y, xPos, y + height, gridPaint);
                }
                if (yPos <= y + height) {
                    canvasStack.canvas.drawLine(x, yPos, x + width, yPos, gridPaint);
                }
            }

            if (width >= 8 && height >= 8) {
                Paint crossPaint = new Paint();
                crossPaint.setColor(0xFFFFFFFF);
                crossPaint.setAntiAlias(true);
                crossPaint.setStrokeWidth(2.0f);
                canvasStack.canvas.drawLine(x + width * 0.25f, y + height * 0.25f,
                        x + width * 0.75f, y + height * 0.75f, crossPaint);
                canvasStack.canvas.drawLine(x + width * 0.75f, y + height * 0.25f,
                        x + width * 0.25f, y + height * 0.75f, crossPaint);

                crossPaint.close();
            }

            backgroundPaint.close();
            gridPaint.close();

        } catch (Exception e) {
            System.err.println("绘制错误头像失败: " + e.getMessage());
            try {
                Paint fallbackPaint = new Paint().setColor(0xFFFF00FF);
                canvasStack.canvas.drawRect(Rect.makeXYWH(x, y, width, height), fallbackPaint);
                fallbackPaint.close();
            } catch (Exception ignored) {
            }
        }
    }
    public static void drawCircularProgress(CanvasStack canvasStack, float centerX, float centerY, float radius, float strokeWidth, float progress, int backgroundColor, int progressColor) {
        centerX = SkiaUtils.transformCoord(centerX);
        centerY = SkiaUtils.transformCoord(centerY);
        radius = SkiaUtils.transformCoord(radius);
        strokeWidth= SkiaUtils.transformCoord(strokeWidth);
        Paint paint = new Paint().setAntiAlias(true).setStrokeWidth(strokeWidth).setStroke(true);
        float left = centerX - radius;
        float top = centerY - radius;
        float right = centerX + radius;
        float bottom = centerY + radius;

        paint.setColor(backgroundColor);
        canvasStack.getCanvas().drawArc(left, top, right, bottom, 0, 360, false, paint);

        paint.setColor(progressColor);
        float sweepAngle = 360f * progress;
        canvasStack.getCanvas().drawArc(left, top, right, bottom, -90, sweepAngle, false, paint); // -90 是从顶部开始
    }

    public static void drawRoundedImage(CanvasStack canvasStack, Image image, float x, float y, float width, float height, float radius) {
        x = SkiaUtils.transformCoord(x);
        y = SkiaUtils.transformCoord(y);
        width = SkiaUtils.transformCoord(width);
        height = SkiaUtils.transformCoord(height);
        Rect dst = Rect.makeXYWH(x, y, width, height);
        Paint paint = new Paint();
        paint.setAntiAlias(true);

        canvasStack.push();

        RRect rrect = RRect.makeXYWH(x, y, width, height, radius);
        canvasStack.canvas.clipRRect(rrect, ClipMode.INTERSECT);

        canvasStack.canvas.drawImageRect(
                image,
                Rect.makeXYWH(0, 0, image.getWidth(), image.getHeight()),
                dst,
                paint
        );

        canvasStack.pop();
    }



    public static void drawRoundedRectStroke(CanvasStack canvasStack,float x, float y, float width, float height, float radius, int color) {
        Paint paint = new Paint();
        paint.setColor(color);
        paint.setMode(PaintMode.STROKE);
        paint.setStrokeWidth(1.0f);
        paint.setAntiAlias(true);
        paint.setStrokeCap(PaintStrokeCap.ROUND);
        paint.setStrokeJoin(PaintStrokeJoin.ROUND);
        x = Math.round(SkiaUtils.transformCoord(x));
        y = Math.round(SkiaUtils.transformCoord(y));
        width = Math.round(SkiaUtils.transformCoord(width));
        height = Math.round(SkiaUtils.transformCoord(height));

        RRect rect = RRect.makeXYWH(x, y, width, height, radius);
        canvasStack.canvas.drawRRect(rect, paint);
    }

    public static void drawRoundedRect(CanvasStack canvasStack, float x, float y, float width, float height, float radius, int color) {
        Paint paint = new Paint()
                .setColor(color)
                .setAntiAlias(true);

        float transformedX = Math.round(SkiaUtils.transformCoord(x));
        float transformedY = Math.round(SkiaUtils.transformCoord(y));
        float transformedWidth = Math.round(SkiaUtils.transformCoord(width));
        float transformedHeight = Math.round(SkiaUtils.transformCoord(height));

        canvasStack.canvas.drawRRect(RRect.makeXYWH(transformedX, transformedY, transformedWidth, transformedHeight, radius), paint);
    }

    public static void drawRoundedRect(CanvasStack canvasStack, float x, float y, float width, float height,
                                       float topLeftRadius, float topRightRadius, float bottomRightRadius, float bottomLeftRadius,
                                       int color) {
        Paint redPaint = new Paint().setColor(color);
        redPaint.setImageFilter(ImageFilter.makeBlur(.5f, .5f, FilterTileMode.DECAL));

        x = SkiaUtils.transformCoord(x);
        y = SkiaUtils.transformCoord(y);
        width = SkiaUtils.transformCoord(width);
        height = SkiaUtils.transformCoord(height);

        float[] radii = {
                topLeftRadius, topLeftRadius,
                topRightRadius, topRightRadius,
                bottomRightRadius, bottomRightRadius,
                bottomLeftRadius, bottomLeftRadius
        };

        RRect rect = RRect.makeComplexXYWH(x, y, width, height, radii);
        canvasStack.canvas.drawRRect(rect, redPaint);
    }

    private static final Paint ROUNDED_RECT_PAINT2 = new Paint().setAntiAlias(false);
    public static void drawBlurRect(CanvasStack canvasStack,float x, float y, float width, float height, float radius, float blurRadius) {
        if (canvasStack == null || canvasStack.canvas == null) {
            System.err.println("CanvasStack或Canvas为null，跳过drawBlurRect渲染");
            return;
        }

        if (canvasStack.context == null) {
            System.err.println("DirectContext为null，跳过drawBlurRect渲染");
            return;
        }

        x = SkiaUtils.transformCoord(x);
        y = SkiaUtils.transformCoord(y);
        width = SkiaUtils.transformCoord(width);
        height = SkiaUtils.transformCoord(height);

        Image image = ImageHelper.getMinecraftAsImage(canvasStack.context, mc.getMainRenderTarget().getColorTextureId(),
                mc.getMainRenderTarget().width,
                mc.getMainRenderTarget().height,
                SurfaceOrigin.BOTTOM_LEFT, false);

        if (image == null) {
            System.err.println("无法获取Minecraft图像，跳过drawBlurRect渲染");
            return;
        }

        Paint blurPaint = ROUNDED_RECT_PAINT2;
        blurPaint.setImageFilter(
                ImageFilter.makeBlur(blurRadius, blurRadius, FilterTileMode.DECAL)
        );
        boolean round = radius != 0;
        canvasStack.push();
        if(round)
            canvasStack.canvas.clipRRect(RRect.makeXYWH(x,y,width,height,radius), ClipMode.INTERSECT);
        else
            canvasStack.canvas.clipRect(Rect.makeXYWH(x,y,width,height), ClipMode.INTERSECT);

        canvasStack.resetTransform();
        canvasStack.canvas.drawImage(image, 0f,0f,blurPaint);
        canvasStack.pop();
    }
    public static void drawBlurRect(CanvasStack canvasStack, float x, float y, float width, float height, float radius, int color) {
        drawBlurRect(canvasStack, x, y, width, height, radius, 2.0f);
    }

    public static void drawRoundedRectWithShadow(CanvasStack canvasStack, float x, float y, float w, float h, float radius) {
        x = Math.round(SkiaUtils.transformCoord(x));
        y = Math.round(SkiaUtils.transformCoord(y));
        w = Math.round(SkiaUtils.transformCoord(w));
        h = Math.round(SkiaUtils.transformCoord(h));
        Paint outerShadow = new Paint()
                .setColor(new java.awt.Color(0, 0, 0, 50).getRGB())
                .setAntiAlias(true)
                .setImageFilter(ImageFilter.makeBlur(20f, 20f, FilterTileMode.DECAL));
        canvasStack.canvas.drawRRect(RRect.makeXYWH(x - 3, y - 3, w + 6, h + 6, radius + 3), outerShadow);
        Paint innerShadow = new Paint()
                .setColor(new java.awt.Color(0, 0, 0, 100).getRGB())
                .setAntiAlias(true)
                .setImageFilter(ImageFilter.makeBlur(6f, 6f, FilterTileMode.DECAL));
        canvasStack.canvas.drawRRect(RRect.makeXYWH(x + 2, y + 2, w, h, radius), innerShadow);
    }

    private static final ConcurrentHashMap<String, Image> imageCache = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, Image> scaledImageCache = new ConcurrentHashMap<>();

    private static final Paint basePaint = createBasePaint();

    private static Paint createBasePaint() {
        Paint p = new Paint();
        p.setAntiAlias(true);
        p.setColor(0xFFFFFFFF);
        p.setColorFilter(ColorFilter.makeLighting(0xFFFFFFFF, 0x000000));
        return p;
    }

    public static void drawHighQualityLogo(CanvasStack canvasStack, String imageLocal,
                                           float x, float y, float width, float height, int color) {
        byte[] bytes = LocalResource.resources.get(imageLocal);
        if (bytes == null) {
            System.out.println(imageLocal + " null");
            return;
        }

        x = SkiaUtils.transformCoord(x);
        y = SkiaUtils.transformCoord(y);
        width = SkiaUtils.transformCoord(width);
        height = SkiaUtils.transformCoord(height);

        Image image = imageCache.computeIfAbsent(imageLocal, key -> {
            try {
                return Image.makeDeferredFromEncodedBytes(bytes);
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        });

        if (image == null || image.isClosed()) {
            return;
        }

        float scaleFactor = Math.min(width / image.getWidth(), height / image.getHeight());

        canvasStack.push();

        if (scaleFactor < 0.5f) {
            int tempWidth = (int) Math.min(width * 2, 1024);
            int tempHeight = (int) Math.min(height * 2, 1024);

            String cacheKey = imageLocal + "_scaled_" + tempWidth + "x" + tempHeight;
            Image scaledImage = scaledImageCache.get(cacheKey);

            if (scaledImage == null || scaledImage.isClosed()) {
                try (Surface tempSurface = Surface.makeRasterN32Premul(tempWidth, tempHeight)) {
                    tempSurface.getCanvas().drawImageRect(
                            image,
                            Rect.makeXYWH(0, 0, image.getWidth(), image.getHeight()),
                            Rect.makeXYWH(0, 0, tempWidth, tempHeight),
                            SamplingMode.CATMULL_ROM,
                            basePaint,
                            true
                    );
                    scaledImage = tempSurface.makeImageSnapshot();
                    if (scaledImage != null) {
                        scaledImageCache.put(cacheKey, scaledImage);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            if (scaledImage != null && !scaledImage.isClosed()) {
                Paint paint = basePaint;
                paint.setColor(color);
                canvasStack.canvas.drawImageRect(
                        scaledImage,
                        Rect.makeXYWH(0, 0, tempWidth, tempHeight),
                        Rect.makeXYWH(x, y, width, height),
                        new FilterMipmap(FilterMode.LINEAR, MipmapMode.LINEAR),
                        paint,
                        true
                );
            }
        } else {
            Paint paint = basePaint;
            paint.setColor(color);

            Rect dst = Rect.makeXYWH(x, y, width, height);
            canvasStack.canvas.drawImageRect(
                    image,
                    Rect.makeXYWH(0, 0, image.getWidth(), image.getHeight()),
                    dst,
                    SamplingMode.CATMULL_ROM,
                    paint,
                    true
            );
        }

        canvasStack.pop();
    }

    private static final ConcurrentHashMap<Integer, cn.cool.cherish.utils.skija.font.SkiaFont> logoFontCache = new ConcurrentHashMap<>();

    public static void drawTTFLogo(CanvasStack canvasStack, String logoChar, 
                                   float x, float y, float size, int color) {
        if (canvasStack == null || canvasStack.canvas == null) {
            return;
        }
        
        if (logoChar == null || logoChar.isEmpty()) {
            logoChar = LOGO_CHAR;
        }
        
        try {
            int fontSize = Math.round(size);
            cn.cool.cherish.utils.skija.font.SkiaFont logoFont = logoFontCache.computeIfAbsent(fontSize, 
                s -> cn.cool.cherish.utils.skija.font.SkiaFont.createSkiaFont("logo.ttf", s));
            logoFont.drawText(canvasStack, logoChar, x, y, color);
            
        } catch (Exception e) {
            drawCircle(canvasStack, x + size/2, y + size/2, size/2, color);
        }
    }

    public static void drawTTFLogo(CanvasStack canvasStack, float x, float y, float size, int color) {
        drawTTFLogo(canvasStack, LOGO_CHAR, x, y, size, color);
    }

    public static void drawRoundedEdgeLine(CanvasStack canvasStack, float x, float startY, float length, float radius, int color, boolean isLeft) {
        x = Math.round(SkiaUtils.transformCoord(x));
        startY = Math.round(SkiaUtils.transformCoord(startY));
        length = Math.round(SkiaUtils.transformCoord(length));
        Paint paint = new Paint();
        paint.setColor(color);
        paint.setMode(PaintMode.STROKE);
        paint.setStrokeWidth(1.0f);
        paint.setAntiAlias(true);
        paint.setStrokeCap(PaintStrokeCap.ROUND);
        paint.setStrokeJoin(PaintStrokeJoin.ROUND);
        canvasStack.canvas.drawLine(x, startY, x, startY + length, paint);
        paint.close();
    }

    public static void drawTabPlayerHead(CanvasStack canvasStack, PlayerInfo playerInfo, float x, float y, float width, float height, float cornerRadius) {
        if (playerInfo == null || canvasStack.canvas == null) {
            drawDefaultHead(canvasStack, x, y, width, height, cornerRadius);
            return;
        }

        try {
            ResourceLocation skinLocation = null;
            try {
                skinLocation = playerInfo.getSkinLocation();
            } catch (Exception e) {
                System.err.println("从PlayerInfo获取皮肤位置失败: " + e.getMessage());
            }
            
            if (skinLocation == null) {
                drawDefaultHead(canvasStack, x, y, width, height, cornerRadius);
                return;
            }
            
            float scaledX = SkiaUtils.transformCoord(x);
            float scaledY = SkiaUtils.transformCoord(y);
            float scaledWidth = SkiaUtils.transformCoord(width);
            float scaledHeight = SkiaUtils.transformCoord(height);
            
            Image headImage = ImageHelper.extractRegion(skinLocation, 8, 8, 8, 8);
            if (headImage == null || headImage.isClosed()) {
                drawDefaultHead(canvasStack, x, y, width, height, cornerRadius);
                return;
            }
            
            Image hatImage = ImageHelper.extractRegion(skinLocation, 40, 8, 8, 8);
            RRect clipRect = RRect.makeXYWH(scaledX, scaledY, scaledWidth, scaledHeight, cornerRadius);
            
            Paint paint = new Paint();
            try (paint) {
                paint.setAntiAlias(true);
                canvasStack.push();
                canvasStack.canvas.clipRRect(clipRect, ClipMode.INTERSECT);
                canvasStack.canvas.drawImageRect(
                        headImage,
                        Rect.makeXYWH(0, 0, headImage.getWidth(), headImage.getHeight()),
                        Rect.makeXYWH(scaledX, scaledY, scaledWidth, scaledHeight),
                        paint
                );
                if (hatImage != null && !hatImage.isClosed()) {
                    Paint hatPaint = new Paint();
                    hatPaint.setAntiAlias(true);
                    hatPaint.setBlendMode(BlendMode.SRC_OVER);

                    canvasStack.canvas.drawImageRect(
                            hatImage,
                            Rect.makeXYWH(0, 0, hatImage.getWidth(), hatImage.getHeight()),
                            Rect.makeXYWH(scaledX, scaledY, scaledWidth, scaledHeight),
                            hatPaint
                    );

                    hatPaint.close();
                }
            } finally {
                canvasStack.pop();
            }
        } catch (Exception e) {
            System.err.println("绘制TabOverlay玩家头像失败: " + e.getMessage());
            e.printStackTrace();
            drawDefaultHead(canvasStack, x, y, width, height, cornerRadius);
        }
    }
    private static void drawDefaultHead(CanvasStack canvasStack, float x, float y, float width, float height, float radius) {
        if (canvasStack == null || canvasStack.canvas == null) {
            return;
        }

        try {
            x = SkiaUtils.transformCoord(x);
            y = SkiaUtils.transformCoord(y);
            width = SkiaUtils.transformCoord(width);
            height = SkiaUtils.transformCoord(height);
            Paint backgroundPaint = new Paint();
            backgroundPaint.setColor(0xFF4A4A4A);
            RRect rrect = RRect.makeXYWH(x, y, width, height, radius);
            canvasStack.canvas.drawRRect(rrect, backgroundPaint);
            Paint iconPaint = new Paint();
            iconPaint.setColor(0xFF888888);
            iconPaint.setAntiAlias(true);
            
            float centerX = x + width / 2;
            float centerY = y + height / 2;
            float iconSize = Math.min(width, height) * 0.6f;
            canvasStack.canvas.drawCircle(centerX, centerY - iconSize * 0.15f, iconSize * 0.25f, iconPaint);
            float bodyWidth = iconSize * 0.4f;
            float bodyHeight = iconSize * 0.35f;
            canvasStack.canvas.drawRect(
                Rect.makeXYWH(centerX - bodyWidth/2, centerY + iconSize * 0.1f, bodyWidth, bodyHeight), 
                iconPaint
            );

            backgroundPaint.close();
            iconPaint.close();

        } catch (Exception e) {
            System.err.println("绘制默认头像失败: " + e.getMessage());
            try {
                Paint fallbackPaint = new Paint().setColor(0xFF4A4A4A);
                canvasStack.canvas.drawRect(Rect.makeXYWH(x, y, width, height), fallbackPaint);
                fallbackPaint.close();
            } catch (Exception ignored) {
            }
        }
    }


}