package cn.cool.cherish.utils.skija.fbo;

import cn.cool.cherish.Client;
import cn.cool.cherish.evnet.EventManager;
import cn.cool.cherish.evnet.EventTarget;
import cn.cool.cherish.evnet.impl.rander.Render2DEvent;
import cn.cool.cherish.evnet.impl.rander.WindowSizeEvent;
import cn.cool.cherish.utils.skija.CanvasStack;
import cn.cool.cherish.utils.skija.SkiaRender;
import cn.cool.cherish.utils.skija.utils.ImageHelper;
import cn.cool.cherish.utils.wrapper.IMinecraft;
import com.mojang.blaze3d.pipeline.RenderTarget;
import com.mojang.blaze3d.pipeline.TextureTarget;
import io.github.humbleui.skija.DirectContext;
import io.github.humbleui.skija.Image;
import io.github.humbleui.skija.SurfaceOrigin;
import lombok.Getter;
import net.minecraft.client.Minecraft;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.function.Consumer;

public class GameFrameBuffer implements IMinecraft {
    @Getter
    private RenderTarget fbo;
    private final DirectContext context;

    public GameFrameBuffer(DirectContext context) {
        this.context = context;
        EventManager.register(this);
    }

    @EventTarget
    public void onWindowChanged(WindowSizeEvent windowSize) {
        resize();
    }

    private final BlockingQueue<Consumer<Render2DEvent>> taskQueue = new LinkedBlockingQueue<>();

    public void execute(Consumer<Render2DEvent> task) {
        if (!taskQueue.offer(task)){
            System.out.println("Error execute task");
        }
    }

    public int getWidth() {
        return fbo.width;
    }

    public int getHeight() {
        return fbo.height;
    }

    public Image read() {
        return ImageHelper.getMinecraftAsImage(context, fbo.getColorTextureId(), getWidth(),
                getHeight(), SurfaceOrigin.BOTTOM_LEFT,true);
    }

    public void draw(CanvasStack canvasStack) {
        draw(canvasStack,0,0, mc.getWindow().getGuiScaledWidth(), mc.getWindow().getGuiScaledHeight());
    }
    
    public void draw(CanvasStack canvasStack,int x,int y, int width, int height) {
        Image image = read();
        SkiaRender.drawImage(canvasStack, image, x, y, width, height);
    }

    public void drawToRegion(CanvasStack canvasStack, int x, int y, int width, int height) {
        Image image = read();
        if (image == null) return;
        SkiaRender.drawImage(canvasStack, image, x, y, width, height);
    }

    public void drawInLocalCoordinates(CanvasStack canvasStack) {
        Image image = read();
        if (image == null) return;
        canvasStack.push();
        canvasStack.resetTransform();
        canvasStack.translate((float) mc.getWindow().getGuiScaledWidth(), (float) mc.getWindow().getGuiScaledHeight());
        canvasStack.canvas.drawImage(image, 0, 0);
        canvasStack.pop();
    }

    @EventTarget
    public void onRender(Render2DEvent event) {
        Consumer<Render2DEvent> task;

        while ((task = taskQueue.poll()) != null) {
            bind();
            task.accept(event);
            end();
        }

    }

    public void bind() {
        if (fbo.width != mc.getMainRenderTarget().width || fbo.height != mc.getMainRenderTarget().height)
            fbo.resize(mc.getMainRenderTarget().width, mc.getMainRenderTarget().height, true);
        fbo.setClearColor(0, 0, 0, 0);
        fbo.clear(Minecraft.ON_OSX);
        fbo.bindWrite(true);
    }

    public void end() {
        mc.getMainRenderTarget().bindWrite(true);
    }

    private void init(int width, int height) {
        fbo = new TextureTarget(width, height,true, Minecraft.ON_OSX);
    }

    public void init() {
        init(mc.getMainRenderTarget().width, mc.getMainRenderTarget().height);
    }

    public void resize() {
        fbo.resize(mc.getMainRenderTarget().width, mc.getMainRenderTarget().height, true);
    }
}
