package cn.cool.cherish.utils.skija.font;

import cn.cool.cherish.Client;
import cn.cool.cherish.evnet.EventManager;
import cn.cool.cherish.evnet.EventTarget;
import cn.cool.cherish.evnet.impl.rander.WindowSizeEvent;
import cn.cool.cherish.utils.skija.CanvasStack;
import cn.cool.cherish.utils.skija.SkiaUtils;
import cn.cool.cherish.utils.wrapper.IMinecraft;
import io.github.humbleui.skija.*;
import net.minecraft.ChatFormatting;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class SkiaFont implements IMinecraft {
    public int originalSize;
    public Font font;
    private final Typeface typeface;
    private float lastSize = -1;

    private static final int MAX_CACHE_SIZE = 500;
    private final Map<String, TextLine> textLineCache = new LinkedHashMap<>(MAX_CACHE_SIZE, 0.75f, true) {
        @Override
        protected boolean removeEldestEntry(Map.Entry<String, TextLine> eldest) {
            boolean shouldRemove = size() > MAX_CACHE_SIZE;
            if (shouldRemove && eldest.getValue() != null) {
                eldest.getValue().close(); // 清理 GPU 资源
            }
            return shouldRemove;
        }
    };

    public SkiaFont(int originalSize, Font font) {
        this.originalSize = originalSize;
        this.font = font;
        this.typeface = font.getTypeface();
        EventManager.register(this);
    }

    public float getHeight() {
        return originalSize / 2f;
    }

    public float getActualHeight() {
        updateFont(Math.round(SkiaUtils.transformCoord((int) (originalSize / 2f))));
        FontMetrics metrics = font.getMetrics();
        float actualHeight = metrics.getHeight();
        return SkiaUtils.transformCoordFromScaled(actualHeight);
    }

    public float getAscent() {
        updateFont(Math.round(SkiaUtils.transformCoord((int) (originalSize / 2f))));
        FontMetrics metrics = font.getMetrics();
        float ascent = Math.abs(metrics._ascent);
        return SkiaUtils.transformCoordFromScaled(ascent);
    }

    public float getDescent() {
        updateFont(Math.round(SkiaUtils.transformCoord((int) (originalSize / 2f))));
        FontMetrics metrics = font.getMetrics();
        float descent = metrics._descent;
        return SkiaUtils.transformCoordFromScaled(descent);
    }

    public float getBaselineOffset() {
        return getAscent();
    }

    @EventTarget
    public void onWindowSize(WindowSizeEvent eventWindowSize) {
        double scaleFactor = (double) mc.getWindow().getWidth() / mc.getWindow().getGuiScaledWidth();
        updateFont(Math.round(SkiaUtils.transformCoord((int) (originalSize / 2f))));
        clearTextLineCache();
    }

    private void clearTextLineCache() {
        for (TextLine textLine : textLineCache.values()) {
            if (textLine != null) {
                textLine.close();
            }
        }
        textLineCache.clear();
    }


    private final Object lock = new Object();

    private TextLine getTextLine(String content) {
        synchronized (lock) {
            TextLine cached = textLineCache.get(content);
            if (cached != null && !cached.isClosed()) {
                return cached;
            }

            TextLine line = TextLine.make(content, font);
            textLineCache.put(content, line);
            return line;
        }
    }

    private void updateFont(float size) {
        if (lastSize != size || font == null) {
            clearTextLineCache();

            font = new Font(typeface, size);
            font.setEdging(FontEdging.ANTI_ALIAS);
            font.setSubpixel(true);
            font.setHinting(FontHinting.NORMAL);
            lastSize = size;
        }
    }

    public static SkiaFont createSkiaFont(int size) {
        double scaleFactor = (double) mc.getWindow().getWidth() / mc.getWindow().getGuiScaledWidth();

        float fontSize = (size / 2f) * (float) scaleFactor;
        Typeface tf = Typeface.makeDefault();
        Font font1 = new Font(tf, fontSize);
        font1.setEdging(FontEdging.ANTI_ALIAS);
        font1.setSubpixel(true);
        font1.setHinting(FontHinting.NORMAL);
        return new SkiaFont(size, font1);
    }

    public static SkiaFont createSkiaFont(String ttf, int size) {
        try {
            double scaleFactor = (double) mc.getWindow().getWidth() / mc.getWindow().getGuiScaledWidth();
            Data data = Data.makeFromFileName(Client.instance.mainFile + "\\resources\\font\\" + ttf);
            Typeface typeface = Typeface.makeFromData(data);
            float fontSize = (size / 2f) * (float) scaleFactor;

            Font font1 = new Font(typeface, fontSize);
            font1.setEdging(FontEdging.ANTI_ALIAS);
            font1.setSubpixel(true);
            font1.setHinting(FontHinting.NORMAL);
            return new SkiaFont(size, font1);
        } catch (Exception e) {
            System.err.println("无法加载字体: " + ttf + "，回退到默认字体");
            return createSkiaFont(size);
        }
    }

    public float getWidth(String text) {
        updateFont(Math.round(SkiaUtils.transformCoord((int) (originalSize / 2f))));

        String plainText = text.replaceAll("§.", "");

        float bounds = font.measureTextWidth(plainText);
        return SkiaUtils.transformCoordFromScaled(bounds);
    }

    private static final Map<Float, ImageFilter> blurFilterCache = new ConcurrentHashMap<>();
    private static final Paint sharedPaint = createSharedPaint();

    private static Paint createSharedPaint() {
        Paint paint = new Paint();
        paint.setAntiAlias(true);
        return paint;
    }

    private static ImageFilter getOrCreateBlur(float radius) {
        if (radius <= 0 || Float.isNaN(radius)) return null;
        return blurFilterCache.computeIfAbsent(radius, r -> ImageFilter.makeBlur(r, r, FilterTileMode.DECAL));
    }

    public void drawTextWithBlur(CanvasStack canvasStack, String text, float x, float y, int color, float blurRadius, boolean shadow) {
        try {
            canvasStack.push();

            x = SkiaUtils.transformCoord(x);
            y = SkiaUtils.transformCoord(y);

            // 使用统一方法更新字体
            updateFont(Math.round(SkiaUtils.transformCoord((int) (originalSize / 2f))));

            float cursorX = x;
            Paint paint = sharedPaint;
            paint.setColor(color);

            ImageFilter filter = getOrCreateBlur(blurRadius);
            paint.setImageFilter(filter);

            String[] parts = text.split("§", -1);
            for (int i = 0; i < parts.length; i++) {
                String part = parts[i];
                if (part.isEmpty()) continue;

                ChatFormatting colorCode = null;
                String content = part;

                if (i > 0) {
                    char codeChar = part.charAt(0);
                    colorCode = ChatFormatting.getByCode(codeChar);
                    content = part.substring(1);
                }

                if (!content.isEmpty()) {
                    float textY = y - font.getMetrics().getAscent();

                    if (shadow) {
                        paint.setColor(0x80000000);
                        canvasStack.canvas.drawString(content, cursorX + 1, textY + 1, font, paint);
                    }

                    int finalColor = (colorCode != null) ? (0xFF000000 | colorCode.getColor()) : color;
                    paint.setColor(finalColor);

                    canvasStack.canvas.drawString(content, cursorX, textY, font, paint);

                    TextLine line = getTextLine(content);
                    cursorX += line.getWidth();
                }
            }

            canvasStack.pop();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void drawTextWithBlur(CanvasStack canvasStack, String text, float x, float y, int color, float blurRadius) {
        drawTextWithBlur(canvasStack, text, x, y, color, blurRadius, false);
    }

    public void drawText(CanvasStack canvasStack, String text, float x, float y, int color) {
        drawTextWithBlur(canvasStack, text, x, y, color, 0, false);
    }

    public void drawText(CanvasStack canvasStack, String text, float x, float y, int color, boolean shadow) {
        if (canvasStack == null || canvasStack.canvas == null) {
            System.err.println("CanvasStack或Canvas为null，跳过drawText渲染");
            return;
        }

        if (text == null || text.isEmpty()) {
            System.err.println("文本为null或空，跳过drawText渲染");
            return;
        }

        if (font == null) {
            System.err.println("字体为null，跳过drawText渲染");
            return;
        }

        canvasStack.push();

        x = SkiaUtils.transformCoord(x);
        y = SkiaUtils.transformCoord(y);

        // 使用统一方法更新字体
        updateFont(Math.round(SkiaUtils.transformCoord((int) (originalSize / 2f))));

        float cursorX = x;
        Paint paint = new Paint();

        paint.setColor(color);
        paint.setAntiAlias(true);

        String[] parts = text.split("§", -1);
        for (int i = 0; i < parts.length; i++) {
            String part = parts[i];
            if (part.isEmpty()) continue;

            ChatFormatting colorCode = null;
            String content = part;

            if (i > 0) {
                char codeChar = part.charAt(0);
                colorCode = ChatFormatting.getByCode(codeChar);
                content = part.substring(1);
            }


            if (!content.isEmpty()) {
                float textY = y - font.getMetrics().getAscent();
                if (shadow) {
                    paint.setColor(0x80000000);
                    canvasStack.canvas.drawString(content, cursorX + 1, textY + 1, font, paint);
                }

                if (colorCode != null) {
                    int rgb = colorCode.getColor();
                    paint.setColor(0xFF000000 | rgb);
                } else {
                    paint.setColor(color);
                }
                canvasStack.canvas.drawString(content, cursorX, textY, font, paint);

                // 使用缓存的TextLine
                TextLine line = getTextLine(content);
                float w = line.getWidth();
                cursorX += w;
            }
        }
        canvasStack.pop();
    }

    @Override
    protected void finalize() throws Throwable {
        clearTextLineCache();
        super.finalize();
    }
}