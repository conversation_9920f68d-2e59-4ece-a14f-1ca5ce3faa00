package cn.cool.cherish.utils.skija.utils;


import cn.cool.cherish.utils.wrapper.IMinecraft;
import com.mojang.blaze3d.platform.NativeImage;
import com.mojang.blaze3d.platform.Window;
import com.mojang.blaze3d.systems.RenderSystem;
import io.github.humbleui.skija.*;
import io.github.humbleui.skija.Image;
import io.github.humbleui.types.Rect;
import net.minecraft.client.renderer.texture.AbstractTexture;
import net.minecraft.client.renderer.texture.TextureManager;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.packs.resources.Resource;
import net.minecraft.server.packs.resources.ResourceManager;
import org.lwjgl.opengl.GL11;
import org.lwjgl.opengl.GL30;

import java.io.InputStream;
import java.nio.ByteBuffer;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.ReentrantLock;

public class ImageHelper implements IMinecraft {

    private static final Map<String, Image> regionCache = new ConcurrentHashMap<>();
    private static final Map<ResourceLocation, NativeImage> skinCache = new ConcurrentHashMap<>();
    private static final Map<String, Image> textures = new ConcurrentHashMap<>();
    private static final ReentrantLock resourceLock = new ReentrantLock(true);
    private static final ReentrantLock renderLock = new ReentrantLock();
    private static Image defaultSkinImage = null;
    private static final AtomicBoolean isProcessing = new AtomicBoolean(false);
    private static final Map<String, Image> errorImageCache = new ConcurrentHashMap<>();


    public static Image extractRegion(ResourceLocation locationSkin, int u, int v, int regionWidth, int regionHeight) {
        if (locationSkin == null) {
            return getDefaultSkin(regionWidth, regionHeight);
        }

        String cacheKey = locationSkin + "_" + u + "_" + v + "_" + regionWidth + "_" + regionHeight;
        Image cachedImage = regionCache.get(cacheKey);
        if (cachedImage != null && !cachedImage.isClosed()) {
            return cachedImage;
        }
        if (isProcessing.get() || !isProcessing.compareAndSet(false, true)) {
            return getDefaultSkin(regionWidth, regionHeight);
        }

        try {
            Image result = null;

            NativeImage fullSkin = getSkinNativeImage(locationSkin);
            if (fullSkin != null) {
                result = extractRegionFromNativeImage(fullSkin, u, v, regionWidth, regionHeight);
            }

            if (result == null && RenderSystem.isOnRenderThread()) {
                result = extractRegionFromTexture(locationSkin, u, v, regionWidth, regionHeight);
            }

            if (result == null) {
                result = getDefaultSkin(regionWidth, regionHeight);
            } else {
                regionCache.put(cacheKey, result);
            }

            return result;
        } catch (Exception e) {
            System.err.println("提取皮肤区域失败: " + e.getMessage());
            return getDefaultSkin(regionWidth, regionHeight);
        } finally {
            isProcessing.set(false);
        }
    }

    private static Image extractRegionFromTexture(ResourceLocation locationSkin, int u, int v, int regionWidth, int regionHeight) {
        if (!RenderSystem.isOnRenderThread()) {
            return null;
        }

        renderLock.lock();
        try {
            TextureManager textureManager = mc.getTextureManager();
            AbstractTexture texture = textureManager.getTexture(locationSkin);

            int textureId = texture.getId();
            RenderSystem.bindTexture(textureId);
            int fbo = -1;
            int previousFBO = GL30.glGetInteger(GL30.GL_FRAMEBUFFER_BINDING);

            try {
                fbo = GL30.glGenFramebuffers();
                GL30.glBindFramebuffer(GL30.GL_FRAMEBUFFER, fbo);
                GL30.glFramebufferTexture2D(GL30.GL_FRAMEBUFFER, GL30.GL_COLOR_ATTACHMENT0, GL11.GL_TEXTURE_2D, textureId, 0);

                int status = GL30.glCheckFramebufferStatus(GL30.GL_FRAMEBUFFER);
                if (status != GL30.GL_FRAMEBUFFER_COMPLETE) {
                    System.err.println("FBO状态不完整: " + status);
                    return null;
                }

                int bufferSize = regionWidth * regionHeight * 4;
                ByteBuffer buffer = ByteBuffer.allocateDirect(bufferSize);
                int previousPack = GL11.glGetInteger(GL11.GL_PACK_ALIGNMENT);
                GL11.glPixelStorei(GL11.GL_PACK_ALIGNMENT, 1);
                GL11.glReadPixels(u, v, regionWidth, regionHeight, GL11.GL_RGBA, GL11.GL_UNSIGNED_BYTE, buffer);
                GL11.glPixelStorei(GL11.GL_PACK_ALIGNMENT, previousPack);
                buffer.rewind();

                byte[] pixelData = new byte[bufferSize];
                for (int i = 0; i < bufferSize; i += 4) {
                    pixelData[i] = buffer.get(i+2);
                    pixelData[i+1] = buffer.get(i+1);
                    pixelData[i+2] = buffer.get(i);
                    pixelData[i+3] = buffer.get(i+3);
                }

                Data data = Data.makeFromBytes(pixelData);
                ImageInfo info = new ImageInfo(regionWidth, regionHeight, ColorType.RGBA_8888, ColorAlphaType.PREMUL);
                Image image = Image.makeRasterFromData(info, data, regionWidth * 4L);

                data.close();
                return image;
            } finally {
                if (fbo != -1) {
                    GL30.glDeleteFramebuffers(fbo);
                }
                GL30.glBindFramebuffer(GL30.GL_FRAMEBUFFER, previousFBO);
            }
        } catch (Exception e) {
            System.err.println("从纹理提取区域失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        } finally {
            renderLock.unlock();
        }
    }


    public static Image extractRegionLocal(ResourceLocation locationSkin, int u, int v, int regionWidth, int regionHeight) {
        return extractRegion(locationSkin, u, v, regionWidth, regionHeight);
    }

    private static NativeImage getSkinNativeImage(ResourceLocation locationSkin) {
        if (locationSkin == null) {
            return null;
        }
        NativeImage cachedSkin = skinCache.get(locationSkin);
        if (cachedSkin != null) {
            return cachedSkin;
        }
        try {
            if (!resourceLock.tryLock(150, java.util.concurrent.TimeUnit.MILLISECONDS)) {
                System.err.println("获取资源锁超时，跳过资源加载: " + locationSkin);
                return null;
            }

            try {
                ResourceManager resourceManager = mc.getResourceManager();

                Optional<Resource> resourceOptional = resourceManager.getResource(locationSkin);
                if (resourceOptional.isPresent()) {
                    Resource resource = resourceOptional.get();
                    try (InputStream stream = resource.open()) {
                        NativeImage nativeImage = null;
                        try {
                            nativeImage = NativeImage.read(NativeImage.Format.RGBA, stream);
                            skinCache.put(locationSkin, nativeImage);
                            return nativeImage;
                        } catch (Exception e) {
                            System.err.println("读取皮肤资源失败: " + e.getMessage());
                            if (nativeImage != null) {
                                nativeImage.close();
                            }
                        }
                    }
                }
            } finally {
                resourceLock.unlock();
            }


            if (RenderSystem.isOnRenderThread()) {
                return getSkinFromRenderThread(locationSkin);
            } else {
                final ResourceLocation finalLocation = locationSkin;
                RenderSystem.recordRenderCall(() -> {
                    getSkinFromRenderThread(finalLocation);
                });
                return null;
            }
        } catch (Exception e) {
            System.err.println("获取皮肤NativeImage失败: " + locationSkin + " - " + e.getMessage());
            return null;
        }
    }

    private static NativeImage getSkinFromRenderThread(ResourceLocation locationSkin) {
        renderLock.lock();
        try {
            NativeImage cachedSkin = skinCache.get(locationSkin);
            if (cachedSkin != null) {
                return cachedSkin;
            }

            TextureManager textureManager = mc.getTextureManager();
            AbstractTexture texture = textureManager.getTexture(locationSkin);

            int previousTexture = GL11.glGetInteger(GL11.GL_TEXTURE_BINDING_2D);

            try {
                RenderSystem.bindTexture(texture.getId());
                int width = 64;
                int height = 64;
                GL11.glGetTexLevelParameteri(GL11.GL_TEXTURE_2D, 0, GL11.GL_TEXTURE_WIDTH);
                if (GL11.glGetError() == 0) {
                    width = GL11.glGetTexLevelParameteri(GL11.GL_TEXTURE_2D, 0, GL11.GL_TEXTURE_WIDTH);
                    height = GL11.glGetTexLevelParameteri(GL11.GL_TEXTURE_2D, 0, GL11.GL_TEXTURE_HEIGHT);
                }

                NativeImage nativeImage = new NativeImage(NativeImage.Format.RGBA, width, height, false);
                try {
                    nativeImage.downloadTexture(0, false);
                    skinCache.put(locationSkin, nativeImage);
                    return nativeImage;
                } catch (Exception e) {
                    System.err.println("下载纹理失败: " + e.getMessage());
                    nativeImage.close();
                    return null;
                }
            } finally {
                RenderSystem.bindTexture(previousTexture);
            }
        } catch (Exception e) {
            System.err.println("从渲染线程获取皮肤失败: " + e.getMessage());
            return null;
        } finally {
            renderLock.unlock();
        }
    }

    public static Image extractRegionFromNativeImage(NativeImage fullImage, int u, int v, int regionWidth, int regionHeight) {
        if (fullImage == null) {
            return null;
        }
        int imageWidth = fullImage.getWidth();
        int imageHeight = fullImage.getHeight();
        if (u < 0 || v < 0 || u + regionWidth > imageWidth || v + regionHeight > imageHeight) {
//            System.err.println("区域超出图像边界: [" + u + "," + v + "," + regionWidth + "," + regionHeight +
//                    "] 图像尺寸: [" + imageWidth + "," + imageHeight + "]");
            u = Math.max(0, Math.min(u, imageWidth - 1));
            v = Math.max(0, Math.min(v, imageHeight - 1));
            regionWidth = Math.min(regionWidth, imageWidth - u);
            regionHeight = Math.min(regionHeight, imageHeight - v);

            if (regionWidth <= 0 || regionHeight <= 0) {
                return null;
            }
        }

        try {
            byte[] pixelData = new byte[regionWidth * regionHeight * 4];
            int index = 0;

            for (int y = v; y < v + regionHeight; y++) {
                for (int x = u; x < u + regionWidth; x++) {
                    int argb = fullImage.getPixelRGBA(x, y);
                    pixelData[index++] = (byte)((argb) & 0xFF);  // B -> R
                    pixelData[index++] = (byte)((argb >> 8) & 0xFF);  // G -> G
                    pixelData[index++] = (byte)((argb >> 16) & 0xFF); // R -> B
                    pixelData[index++] = (byte)((argb >> 24) & 0xFF); // A -> A
                }
            }

            try (Data data = Data.makeFromBytes(pixelData);
                 Surface surface = Surface.makeRasterN32Premul(regionWidth, regionHeight)) {
                ImageInfo info = new ImageInfo(regionWidth, regionHeight, ColorType.RGBA_8888, ColorAlphaType.UNPREMUL);
                Image tempImage = Image.makeRasterFromData(info, data, regionWidth * 4L);
                surface.getCanvas().drawImage(tempImage, 0, 0);
                tempImage.close();
                return surface.makeImageSnapshot();
            }
        } catch (Exception e) {
            System.err.println("提取区域失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    private static Image getDefaultSkin(int width, int height) {
        String key = width + "x" + height;
        Image cachedErrorImage = errorImageCache.get(key);
        if (cachedErrorImage != null && !cachedErrorImage.isClosed()) {
            return cachedErrorImage;
        }

        if (width == 8 && height == 8 && defaultSkinImage != null && !defaultSkinImage.isClosed()) {
            return defaultSkinImage;
        }
        Image errorImage = createErrorImage(width, height);
        errorImageCache.put(key, errorImage);

        if (width == 8 && height == 8) {
            defaultSkinImage = errorImage;
        }

        return errorImage;
    }

    private static Image createErrorImage(int width, int height) {
        try {
            Surface surface = Surface.makeRasterN32Premul(width, height);
            if (surface == null) {
                return null;
            }

            io.github.humbleui.skija.Canvas canvas = surface.getCanvas();
            int gridSize = Math.max(width, height) / 4;
            gridSize = Math.max(1, gridSize);
            io.github.humbleui.skija.Paint purplePaint = new io.github.humbleui.skija.Paint();
            purplePaint.setColor(new io.github.humbleui.skija.Color4f(1.0f, 0.0f, 1.0f, 0.6f).toColor()); // 半透明紫色
            io.github.humbleui.skija.Paint transparentPaint = new io.github.humbleui.skija.Paint();
            transparentPaint.setColor(new io.github.humbleui.skija.Color4f(0.0f, 0.0f, 0.0f, 0.0f).toColor());  // 透明色

            for (int y = 0; y < height; y += gridSize) {
                for (int x = 0; x < width; x += gridSize) {
                    Rect rect = Rect.makeXYWH(x, y, gridSize, gridSize);
                    if ((x / gridSize + y / gridSize) % 2 == 0) {
                        canvas.drawRect(rect, purplePaint);
                    } else {
                        canvas.drawRect(rect, transparentPaint);
                    }
                }
            }

            purplePaint.close();
            transparentPaint.close();
            Image result = surface.makeImageSnapshot();
            surface.close();
            return result;
        } catch (Exception e) {
            System.err.println("创建错误图像失败: " + e.getMessage());
            try {
                Surface surface = Surface.makeRasterN32Premul(width, height);
                if (surface == null) {
                    return null;
                }
                surface.getCanvas().clear(0xFFFF00FF);
                Image result = surface.makeImageSnapshot();
                surface.close();
                return result;
            } catch (Exception ex) {
                return null;
            }
        }
    }

    public static Image getMinecraftAsImage(DirectContext context, int tex, int width, int height, SurfaceOrigin origin, boolean alpha) {
        if (context == null || !RenderSystem.isOnRenderThread()) {
            return null;
        }

        renderLock.lock();
        try {
            RenderSystem.bindTexture(tex);
            Window window = mc.getWindow();
            int currentWidth = width;
            int currentHeight = height;
            if (tex == mc.getMainRenderTarget().getColorTextureId()) {
                currentWidth = window.getWidth();
                currentHeight = window.getHeight();
            }
            String cacheKey = tex + "_" + currentWidth + "_" + currentHeight;
            Image img = textures.get(cacheKey);
            boolean needsRecreate = img == null || img.isClosed() ||
                    img.getWidth() != currentWidth ||
                    img.getHeight() != currentHeight;

            if (needsRecreate) {
                if (img != null && !img.isClosed()) {
                    try {
                        img.close();
                        textures.remove(cacheKey);
                    } catch (Exception ignored) {}
                }

                try {
                    int textureId = tex;
                    if (tex == mc.getMainRenderTarget().getColorTextureId()) {
                        textureId = GL11.glGetInteger(GL11.GL_TEXTURE_BINDING_2D);
                    }
                    img = Image.adoptGLTextureFrom(
                            context,
                            textureId,
                            GL11.GL_TEXTURE_2D,
                            currentWidth,
                            currentHeight,
                            GL11.GL_RGBA8,
                            origin,
                            alpha ? ColorType.RGBA_8888 : ColorType.RGB_888X
                    );

                    textures.put(cacheKey, img);
                } catch (Exception e) {
                    System.err.println("获取Minecraft图像失败: " + e.getMessage());
                    e.printStackTrace();
                    return null;
                }
            }
            return img;
        } catch (Exception e) {
            System.err.println("获取Minecraft图像失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        } finally {
            renderLock.unlock();
        }
    }
    public static void clearCache() {
        renderLock.lock();
        try {
            clearImageMap(regionCache, "区域缓存");
            regionCache.clear();
            for (Map.Entry<ResourceLocation, NativeImage> entry : skinCache.entrySet()) {
                try {
                    NativeImage image = entry.getValue();
                    if (image != null) {
                        image.close();
                    }
                } catch (Exception e) {
                    System.err.println("清理皮肤缓存失败: " + e.getMessage());
                }
            }
            skinCache.clear();
            clearImageMap(textures, "纹理缓存");
            textures.clear();
            clearImageMap(errorImageCache, "错误图像缓存");
            errorImageCache.clear();
            if (defaultSkinImage != null) {
                try {
                    if (!defaultSkinImage.isClosed()) {
                        defaultSkinImage.close();
                    }
                } catch (Exception e) {
                    System.err.println("清理默认皮肤失败: " + e.getMessage());
                }
                defaultSkinImage = null;
            }

            isProcessing.set(false);
            System.gc();
        } finally {
            renderLock.unlock();
        }
    }

    private static void clearImageMap(Map<?, Image> map, String mapName) {
        if (map == null) return;

        int closedCount = 0;
        int failedCount = 0;

        for (Map.Entry<?, Image> entry : map.entrySet()) {
            try {
                Image image = entry.getValue();
                if (image != null && !image.isClosed()) {
                    image.close();
                    closedCount++;
                }
            } catch (Exception e) {
                failedCount++;
                System.err.println("清理图像失败 (" + mapName + "): " + e.getMessage());
            }
        }

        if (closedCount > 0 || failedCount > 0) {
            System.out.println("已清理 " + mapName + ": 成功=" + closedCount + ", 失败=" + failedCount);
        }
    }

    public static void refreshSkin(ResourceLocation skinLocation) {
        if (skinLocation == null) return;

        renderLock.lock();
        try {
            regionCache.entrySet().removeIf(entry -> entry.getKey().startsWith(skinLocation.toString()));
            NativeImage oldSkin = skinCache.remove(skinLocation);
            if (oldSkin != null) {
                try {
                    oldSkin.close();
                } catch (Exception e) {
                    System.err.println("关闭皮肤图像失败: " + e.getMessage());
                }
            }

            System.out.println("已刷新皮肤缓存: " + skinLocation);
        } finally {
            renderLock.unlock();
        }
    }
    public static void refreshAllSkins() {
        renderLock.lock();
        try {
            clearImageMap(regionCache, "区域缓存");
            regionCache.clear();
            for (Map.Entry<ResourceLocation, NativeImage> entry : skinCache.entrySet()) {
                try {
                    NativeImage image = entry.getValue();
                    if (image != null) {
                        image.close();
                    }
                } catch (Exception e) {
                    System.err.println("清理皮肤缓存失败: " + e.getMessage());
                }
            }
            skinCache.clear();

            System.out.println("已刷新所有皮肤缓存");
        } finally {
            renderLock.unlock();
        }
    }
} 