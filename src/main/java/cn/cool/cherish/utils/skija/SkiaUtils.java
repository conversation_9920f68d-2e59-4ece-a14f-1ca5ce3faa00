package cn.cool.cherish.utils.skija;

import cn.cool.cherish.Client;
import cn.cool.cherish.utils.skija.state.GLStateStack;
import cn.cool.cherish.utils.skija.utils.ImageHelper;
import cn.cool.cherish.utils.wrapper.IMinecraft;
import io.github.humbleui.skija.*;
import org.lwjgl.opengl.GL11;

public class SkiaUtils implements IMinecraft {

    public Surface surface;
    public BackendRenderTarget renderTarget;
    public Canvas canvas;
    public DirectContext context;

    private int currentSurfaceWidth = 0;
    private int currentSurfaceHeight = 0;

    public void initSkia() {
        try {
            createContext();
            createSurface();
            Client.logger.info("initSkia Success!");
        } catch (Exception e) {
            System.err.println("Skija初始化失败: " + e.getMessage());
        }
    }

    private void createContext() {
        if (context == null) {
            try {
                context = DirectContext.makeGL();
                if (context == null) {
                    System.err.println("DirectContext.makeGL()返回null");
                } else {
                    System.out.println("DirectContext创建成功");
                }
            } catch (Exception e) {
                System.err.println("创建DirectContext失败: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    private void createSurface() {
        try {
            if (surface != null) {
                surface.close();
                surface = null;
            }
            if (renderTarget != null) {
                renderTarget.close();
                renderTarget = null;
            }

            int width = (mc.getMainRenderTarget().width);
            int height = (mc.getMainRenderTarget().height);

            System.out.println("创建Surface，尺寸: " + width + "x" + height);
            renderTarget = BackendRenderTarget.makeGL(
                    width,
                    height,
                    0,
                    8,
                    0,
                    FramebufferFormat.GR_GL_RGBA8
            );

            if (renderTarget == null) {
                System.err.println("BackendRenderTarget.makeGL()返回null");
                return;
            }

            System.out.println("BackendRenderTarget创建成功");

            surface = Surface.wrapBackendRenderTarget(
                    context,
                    renderTarget,
                    SurfaceOrigin.BOTTOM_LEFT,
                    SurfaceColorFormat.RGBA_8888,
                    ColorSpace.getSRGB()
            );

            if (surface == null) {
                System.err.println("Surface.wrapBackendRenderTarget()返回null");
                return;
            }

            System.out.println("Surface创建成功");

            canvas = surface.getCanvas();

            if (canvas == null) {
                System.err.println("surface.getCanvas()返回null");
                return;
            }

            System.out.println("Canvas获取成功");

            // 保存当前尺寸
            currentSurfaceWidth = width;
            currentSurfaceHeight = height;
        } catch (Exception e) {
            System.err.println("创建Surface失败: " + e.getMessage());
            e.printStackTrace();
        }
    }


    public void beginFrame() {
        if (context == null || surface == null) {
            throw new IllegalStateException("Skija未正确初始化");
        }

        GLStateStack.push();


        GL11.glDisable(GL11.GL_CULL_FACE);
        GL11.glClearColor(0f, 0f, 0f, 0f);


        resetSelectiveGLStates();
    }

    public void endFrame() {
        try {
            if (surface != null) {
                surface.flushAndSubmit();
            }
        } finally {
            // 恢复OpenGL状态
            try {
                GLStateStack.pop();
            } catch (Exception e) {
                System.err.println("恢复OpenGL状态时出错: " + e.getMessage());
                GLStateStack.clear(); // 清空栈以防止状态污染
            }
        }
    }

    private void resetSelectiveGLStates() {
        if (context != null) {
            GLBackendState[] states = {
                    GLBackendState.BLEND,
                    GLBackendState.VERTEX,
                    GLBackendState.PIXEL_STORE,
                    GLBackendState.TEXTURE_BINDING,
                    GLBackendState.MISC
            };
            context.resetGL(states);
        }
    }

    public static float transformCoord(float coordinate) {
        int guiScale = (int) mc.getWindow().getGuiScale();
        return (float) Math.ceil(coordinate * guiScale);
    }

    public static float transformCoordFromScaled(float scaledCoordinate) {
        int realWidth = mc.getWindow().getWidth();
        int scaledWidth = mc.getWindow().getGuiScaledWidth();
        double ratio = (double) scaledWidth / realWidth;
        return (float) (scaledCoordinate * ratio);
    }

    public void checkAndUpdateSurface() {
        if (surface != null && renderTarget != null) {
            int newWidth = (mc.getMainRenderTarget().width);
            int newHeight = (mc.getMainRenderTarget().height);

            if (currentSurfaceWidth != newWidth || currentSurfaceHeight != newHeight) {
                createSurface();
            }
        }
    }

    public void cleanup() {
        if (surface != null) {
            surface.close();
            surface = null;
        }
        if (renderTarget != null) {
            renderTarget.close();
            renderTarget = null;
        }
        if (context != null) {
            context.close();
            context = null;
        }
        canvas = null;

        ImageHelper.clearCache();
    }


}
