package cn.cool.cherish.utils.skija;

import io.github.humbleui.skija.*;
import lombok.Getter;

public class CanvasStack {
    @Getter
    public final Canvas canvas;

    public DirectContext context;
    public Surface surface;
    @Getter
    private final int width;
    @Getter
    private final int height;



    public CanvasStack(SkiaUtils skiaUtils) {
        this.canvas = skiaUtils.canvas;
        this.context = skiaUtils.context;
        surface = skiaUtils.surface;
        this.width = surface.getWidth();
        this.height = surface.getHeight();

    }

    public void resetTransform() {
        canvas.setMatrix(Matrix33.IDENTITY);
    }
    public void push() {
        canvas.save();
        canvas.translate(0,0);
    }
    public void pop() {
        canvas.restore();
    }

    public void translate(float x, float y) {
        x = SkiaUtils.transformCoord(x);
        y = SkiaUtils.transformCoord(y);
        canvas.translate(x, y);
    }



}
