package cn.cool.cherish.utils.skija;

import cn.cool.cherish.Client;
import cn.cool.cherish.evnet.EventManager;
import cn.cool.cherish.evnet.EventTarget;
import cn.cool.cherish.evnet.impl.rander.EventRenderSkija;
import cn.cool.cherish.evnet.impl.rander.EventSkiaProcess;
import cn.cool.cherish.utils.skija.utils.ImageHelper;

public class SkiaProcess extends Skia {
    public static SkiaProcess INSTANCE;

    public SkiaProcess() {
        EventManager.register(this);

        INSTANCE = this;  // 设置静态实例
    }

    @EventTarget
    public void onRender(EventRenderSkija eventRenderSkija) {

        if (skiaUtils == null) {
            System.err.println("SkijaUtils not initialized. Skipping render frame.");
            return;
        }

        if (skiaUtils.context == null) {
            System.err.println("DirectContext为null，跳过渲染帧");
            return;
        }

        if (skiaUtils.surface == null) {
            System.err.println("Surface为null，跳过渲染帧");
            return;
        }

        if (skiaUtils.canvas == null) {
            System.err.println("Canvas为null，跳过渲染帧");
            return;
        }

        try {
            skiaUtils.checkAndUpdateSurface();
            skiaUtils.beginFrame();

            CanvasStack canvasStack = new CanvasStack(skiaUtils);
            if (canvasStack.canvas == null) {
                System.err.println("CanvasStack中的Canvas为null，跳过渲染");
                return;
            }

            canvasStack.push();

            try {
                if (mc.level != null || mc.player != null) {
                    EventManager.call(new EventSkiaProcess(canvasStack));
                }

                // processRenderTasks(canvasStack); // Uncomment if needed
            } finally {
                canvasStack.pop();
            }

            skiaUtils.endFrame();
            // updatePerformanceStats(); // Uncomment if needed

        } catch (Exception e) {
            System.err.println("Error during Skia rendering: " + e.getMessage());
            e.printStackTrace();
            try {
                skiaUtils.endFrame();
            } catch (Exception ignored) {
                System.err.println("Warning: Error occurred during Skia endFrame cleanup after a rendering error: " + ignored.getMessage());
            }
        }

    }

    public void cleanup() {

        ImageHelper.clearCache();
        if (skiaUtils != null) {
            skiaUtils.cleanup();
        }

        System.out.println("SkijaProcess资源已清理");
    }

}
