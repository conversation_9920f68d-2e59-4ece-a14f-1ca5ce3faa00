package cn.cool.cherish.utils.skija.state;

import org.lwjgl.opengl.*;

public class GLState {

    private static final int GL_VERSION;

    private final int[] lastActiveTexture = new int[1];
    private final int[] lastProgram = new int[1];
    private final int[] lastTexture = new int[1];
    private final int[] lastSampler = new int[1];
    private final int[] lastArrayBuffer = new int[1];
    private final int[] lastVertexArrayObject = new int[1];
    private final int[] lastPolygonMode = new int[1];
    private final int[] lastViewport = new int[4];
    private final int[] lastScissorBox = new int[4];
    private final int[] lastBlendSrcRgb = new int[1];
    private final int[] lastBlendDstRgb = new int[1];
    private final int[] lastBlendSrcAlpha = new int[1];
    private final int[] lastBlendDstAlpha = new int[1];
    private final int[] lastBlendEquationRgb = new int[1];
    private final int[] lastBlendEquationAlpha = new int[1];
    
    // 布尔状态
    private boolean lastEnableBlend;
    private boolean lastEnableCullFace;
    private boolean lastEnableDepthTest;
    private boolean lastEnableStencilTest;
    private boolean lastEnableScissorTest;
    private boolean lastEnablePrimitiveRestart;
    private boolean lastDepthMask;
    
    // 像素存储状态
    private final int[] lastPixelUnpackBufferBinding = new int[1];
    private final int[] lastPackSwapBytes = new int[1];
    private final int[] lastPackLsbFirst = new int[1];
    private final int[] lastPackRowLength = new int[1];
    private final int[] lastPackSkipPixels = new int[1];
    private final int[] lastPackSkipRows = new int[1];
    private final int[] lastPackAlignment = new int[1];
    private final int[] lastUnpackSwapBytes = new int[1];
    private final int[] lastUnpackLsbFirst = new int[1];
    private final int[] lastUnpackAlignment = new int[1];
    private final int[] lastUnpackRowLength = new int[1];
    private final int[] lastUnpackSkipPixels = new int[1];
    private final int[] lastUnpackSkipRows = new int[1];
    private final int[] lastPackImageHeight = new int[1];
    private final int[] lastPackSkipImages = new int[1];
    private final int[] lastUnpackImageHeight = new int[1];
    private final int[] lastUnpackSkipImages = new int[1];
    
    static {
        int[] major = new int[1];
        int[] minor = new int[1];
        GL11.glGetIntegerv(GL30.GL_MAJOR_VERSION, major);
        GL11.glGetIntegerv(GL30.GL_MINOR_VERSION, minor);
        GL_VERSION = major[0] * 100 + minor[0] * 10;
    }
    
    /**
     * 保存当前OpenGL状态
     */
    public GLState push() {
        GL11.glGetIntegerv(GL13.GL_ACTIVE_TEXTURE, lastActiveTexture);
        GL13.glActiveTexture(GL13.GL_TEXTURE0);
        GL11.glGetIntegerv(GL20.GL_CURRENT_PROGRAM, lastProgram);
        GL11.glGetIntegerv(GL11.GL_TEXTURE_BINDING_2D, lastTexture);
        
        if (GL_VERSION >= 330 || GL.getCapabilities().GL_ARB_sampler_objects) {
            GL11.glGetIntegerv(GL33.GL_SAMPLER_BINDING, lastSampler);
        }
        
        GL11.glGetIntegerv(GL15.GL_ARRAY_BUFFER_BINDING, lastArrayBuffer);
        GL11.glGetIntegerv(GL30.GL_VERTEX_ARRAY_BINDING, lastVertexArrayObject);
        
        if (GL_VERSION >= 200) {
            GL11.glGetIntegerv(GL11.GL_POLYGON_MODE, lastPolygonMode);
        }
        
        GL11.glGetIntegerv(GL11.GL_VIEWPORT, lastViewport);
        GL11.glGetIntegerv(GL11.GL_SCISSOR_BOX, lastScissorBox);
        GL11.glGetIntegerv(GL11.GL_BLEND_SRC, lastBlendSrcRgb);
        GL11.glGetIntegerv(GL11.GL_BLEND_DST, lastBlendDstRgb);
        GL11.glGetIntegerv(GL14.GL_BLEND_SRC_ALPHA, lastBlendSrcAlpha);
        GL11.glGetIntegerv(GL14.GL_BLEND_DST_ALPHA, lastBlendDstAlpha);
        GL11.glGetIntegerv(GL20.GL_BLEND_EQUATION_RGB, lastBlendEquationRgb);
        GL11.glGetIntegerv(GL20.GL_BLEND_EQUATION_ALPHA, lastBlendEquationAlpha);
        
        lastEnableBlend = GL11.glIsEnabled(GL11.GL_BLEND);
        lastEnableCullFace = GL11.glIsEnabled(GL11.GL_CULL_FACE);
        lastEnableDepthTest = GL11.glIsEnabled(GL11.GL_DEPTH_TEST);
        lastEnableStencilTest = GL11.glIsEnabled(GL11.GL_STENCIL_TEST);
        lastEnableScissorTest = GL11.glIsEnabled(GL11.GL_SCISSOR_TEST);
        
        if (GL_VERSION >= 310) {
            lastEnablePrimitiveRestart = GL11.glIsEnabled(GL31.GL_PRIMITIVE_RESTART);
        }
        
        lastDepthMask = GL11.glGetBoolean(GL11.GL_DEPTH_WRITEMASK);
        
        // 像素存储状态
        GL11.glGetIntegerv(GL21.GL_PIXEL_UNPACK_BUFFER_BINDING, lastPixelUnpackBufferBinding);
        GL15.glBindBuffer(GL21.GL_PIXEL_UNPACK_BUFFER, 0);
        
        GL11.glGetIntegerv(GL11.GL_PACK_SWAP_BYTES, lastPackSwapBytes);
        GL11.glGetIntegerv(GL11.GL_PACK_LSB_FIRST, lastPackLsbFirst);
        GL11.glGetIntegerv(GL11.GL_PACK_ROW_LENGTH, lastPackRowLength);
        GL11.glGetIntegerv(GL11.GL_PACK_SKIP_PIXELS, lastPackSkipPixels);
        GL11.glGetIntegerv(GL11.GL_PACK_SKIP_ROWS, lastPackSkipRows);
        GL11.glGetIntegerv(GL11.GL_PACK_ALIGNMENT, lastPackAlignment);
        
        GL11.glGetIntegerv(GL11.GL_UNPACK_SWAP_BYTES, lastUnpackSwapBytes);
        GL11.glGetIntegerv(GL11.GL_UNPACK_LSB_FIRST, lastUnpackLsbFirst);
        GL11.glGetIntegerv(GL11.GL_UNPACK_ALIGNMENT, lastUnpackAlignment);
        GL11.glGetIntegerv(GL11.GL_UNPACK_ROW_LENGTH, lastUnpackRowLength);
        GL11.glGetIntegerv(GL11.GL_UNPACK_SKIP_PIXELS, lastUnpackSkipPixels);
        GL11.glGetIntegerv(GL11.GL_UNPACK_SKIP_ROWS, lastUnpackSkipRows);
        
        if (GL_VERSION >= 120) {
            GL11.glGetIntegerv(GL12.GL_PACK_IMAGE_HEIGHT, lastPackImageHeight);
            GL11.glGetIntegerv(GL12.GL_PACK_SKIP_IMAGES, lastPackSkipImages);
            GL11.glGetIntegerv(GL12.GL_UNPACK_IMAGE_HEIGHT, lastUnpackImageHeight);
            GL11.glGetIntegerv(GL12.GL_UNPACK_SKIP_IMAGES, lastUnpackSkipImages);
        }
        
        // 设置Skija需要的像素存储状态
        GL11.glPixelStorei(GL11.GL_UNPACK_ALIGNMENT, 1);
        GL11.glPixelStorei(GL11.GL_UNPACK_ROW_LENGTH, 0);
        GL11.glPixelStorei(GL11.GL_UNPACK_SKIP_PIXELS, 0);
        GL11.glPixelStorei(GL11.GL_UNPACK_SKIP_ROWS, 0);
        
        return this;
    }
    
    /**
     * 恢复之前保存的OpenGL状态
     */
    public GLState pop() {
        GL20.glUseProgram(lastProgram[0]);
        GL11.glBindTexture(GL11.GL_TEXTURE_2D, lastTexture[0]);
        
        if (GL_VERSION >= 330 || GL.getCapabilities().GL_ARB_sampler_objects) {
            GL33.glBindSampler(0, lastSampler[0]);
        }
        
        GL13.glActiveTexture(lastActiveTexture[0]);
        GL30.glBindVertexArray(lastVertexArrayObject[0]);
        GL15.glBindBuffer(GL15.GL_ARRAY_BUFFER, lastArrayBuffer[0]);
        
        GL20.glBlendEquationSeparate(lastBlendEquationRgb[0], lastBlendEquationAlpha[0]);
        GL14.glBlendFuncSeparate(lastBlendSrcRgb[0], lastBlendDstRgb[0], 
                                lastBlendSrcAlpha[0], lastBlendDstAlpha[0]);
        
        if (lastEnableBlend) GL11.glEnable(GL11.GL_BLEND);
        else GL11.glDisable(GL11.GL_BLEND);
        
        if (lastEnableCullFace) GL11.glEnable(GL11.GL_CULL_FACE);
        else GL11.glDisable(GL11.GL_CULL_FACE);
        
        if (lastEnableDepthTest) GL11.glEnable(GL11.GL_DEPTH_TEST);
        else GL11.glDisable(GL11.GL_DEPTH_TEST);
        
        if (lastEnableStencilTest) GL11.glEnable(GL11.GL_STENCIL_TEST);
        else GL11.glDisable(GL11.GL_STENCIL_TEST);
        
        if (lastEnableScissorTest) GL11.glEnable(GL11.GL_SCISSOR_TEST);
        else GL11.glDisable(GL11.GL_SCISSOR_TEST);
        
        if (GL_VERSION >= 310) {
            if (lastEnablePrimitiveRestart) GL11.glEnable(GL31.GL_PRIMITIVE_RESTART);
            else GL11.glDisable(GL31.GL_PRIMITIVE_RESTART);
        }
        
        if (GL_VERSION >= 200) {
            GL11.glPolygonMode(GL11.GL_FRONT_AND_BACK, lastPolygonMode[0]);
        }
        
        GL11.glViewport(lastViewport[0], lastViewport[1], lastViewport[2], lastViewport[3]);
        GL11.glScissor(lastScissorBox[0], lastScissorBox[1], lastScissorBox[2], lastScissorBox[3]);
        
        // 恢复像素存储状态
        GL11.glPixelStorei(GL11.GL_PACK_SWAP_BYTES, lastPackSwapBytes[0]);
        GL11.glPixelStorei(GL11.GL_PACK_LSB_FIRST, lastPackLsbFirst[0]);
        GL11.glPixelStorei(GL11.GL_PACK_ROW_LENGTH, lastPackRowLength[0]);
        GL11.glPixelStorei(GL11.GL_PACK_SKIP_PIXELS, lastPackSkipPixels[0]);
        GL11.glPixelStorei(GL11.GL_PACK_SKIP_ROWS, lastPackSkipRows[0]);
        GL11.glPixelStorei(GL11.GL_PACK_ALIGNMENT, lastPackAlignment[0]);
        
        GL15.glBindBuffer(GL21.GL_PIXEL_UNPACK_BUFFER, lastPixelUnpackBufferBinding[0]);
        GL11.glPixelStorei(GL11.GL_UNPACK_SWAP_BYTES, lastUnpackSwapBytes[0]);
        GL11.glPixelStorei(GL11.GL_UNPACK_LSB_FIRST, lastUnpackLsbFirst[0]);
        GL11.glPixelStorei(GL11.GL_UNPACK_ALIGNMENT, lastUnpackAlignment[0]);
        GL11.glPixelStorei(GL11.GL_UNPACK_ROW_LENGTH, lastUnpackRowLength[0]);
        GL11.glPixelStorei(GL11.GL_UNPACK_SKIP_PIXELS, lastUnpackSkipPixels[0]);
        GL11.glPixelStorei(GL11.GL_UNPACK_SKIP_ROWS, lastUnpackSkipRows[0]);
        
        if (GL_VERSION >= 120) {
            GL11.glPixelStorei(GL12.GL_PACK_IMAGE_HEIGHT, lastPackImageHeight[0]);
            GL11.glPixelStorei(GL12.GL_PACK_SKIP_IMAGES, lastPackSkipImages[0]);
            GL11.glPixelStorei(GL12.GL_UNPACK_IMAGE_HEIGHT, lastUnpackImageHeight[0]);
            GL11.glPixelStorei(GL12.GL_UNPACK_SKIP_IMAGES, lastUnpackSkipImages[0]);
        }
        
        // 关键修复：恢复深度掩码状态，修复文本闪烁和窗口调整问题
        GL11.glDepthMask(lastDepthMask);
        
        return this;
    }
} 