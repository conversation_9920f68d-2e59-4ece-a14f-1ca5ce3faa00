package cn.cool.cherish.utils.client;

import cn.cool.cherish.Client;
import cn.cool.cherish.utils.wrapper.IMinecraft;
import lombok.experimental.UtilityClass;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;

@UtilityClass
public class ClientUtil implements IMinecraft {
    String prefix = ChatFormatting.DARK_GRAY + "[" + Client.instance.name + ChatFormatting.DARK_GRAY + "] " + ChatFormatting.RESET;

    public void addChatMessage(String message) {
        if (mc.player == null) return;

        mc.player.displayClientMessage(Component.literal(message), false);
    }

    public void log(String message) {
        addChatMessage(prefix + message);
    }
}
