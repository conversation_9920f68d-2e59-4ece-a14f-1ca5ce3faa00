package cn.cool.cherish.command.commands;

import cn.cool.cherish.Client;
import cn.cool.cherish.command.Command;
import cn.cool.cherish.module.Module;
import cn.cool.cherish.utils.client.ClientUtil;

public class Toggle extends Command {
    public Toggle() {
        super("toggle", "t");
    }

    @Override
    public void execute(String[] params) {
        if (params.length < 1) {
            ClientUtil.log("Usage: .toggle <module> [on/off]");
            return;
        }

        final Module module = Client.instance.getModuleManager().getModuleByName(params[0]);

        if (module == null) {
            ClientUtil.log("Module '" + params[0] + "' not found.");
            return;
        }

        boolean enable = !module.isEnabled();

        if (params.length == 2) {
            enable = switch (params[1].toLowerCase()) {
                case "on", "enable", "yes", "true", "1" -> true;
                case "off", "disable", "no", "false", "0" -> false;
                default -> {
                    ClientUtil.log("Invalid parameter. Use: on/off, enable/disable, yes/no, true/false, 1/0");
                    yield enable;
                }
            };
        }

        module.setEnabled(enable);
        ClientUtil.log("Module '" + module.getName() + "' " + (enable ? "enabled" : "disabled") + ".");
    }
}