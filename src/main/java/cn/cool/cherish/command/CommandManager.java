package cn.cool.cherish.command;

import cn.cool.cherish.Client;
import cn.cool.cherish.command.commands.*;
import cn.cool.cherish.evnet.EventManager;
import cn.cool.cherish.evnet.EventTarget;
import cn.cool.cherish.evnet.impl.packet.PacketEvent;
import cn.cool.cherish.utils.client.ClientUtil;
import lombok.Getter;
import net.minecraft.network.protocol.Packet;
import net.minecraft.network.protocol.game.ServerboundChatPacket;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@Getter
public class CommandManager {
    private final Map<String,Command> commands = new HashMap<>();

    public CommandManager() {
        EventManager.register(this);

        registerCommand(new Toggle());
    }

    private void registerCommand(Command command) {
        for (String name : command.getName()) {
            commands.put(name.toLowerCase(), command);
        }
    }

    @EventTarget
    public void onChat(PacketEvent event) {
        Packet<?> packet = event.getPacket();

        if (packet instanceof ServerboundChatPacket wrapper) {
            if (wrapper.message().startsWith(".")) {
                String[] ars = wrapper.message().substring(1).split(" ");
                String name = ars[0];
                Command command = commands.get(name.toLowerCase());

                if (command == null) {
                    ClientUtil.log("Error: " + name + " is not a command.");
                } else {
                    command.execute(Arrays.copyOfRange(ars, 1, ars.length));
                }
                event.setCancelled(true);
            }
        }
    }
}
