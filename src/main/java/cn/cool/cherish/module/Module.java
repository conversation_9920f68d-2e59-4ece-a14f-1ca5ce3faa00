package cn.cool.cherish.module;

import cn.cool.cherish.Client;
import cn.cool.cherish.evnet.EventManager;
import cn.cool.cherish.utils.wrapper.IMinecraft;
import lombok.Getter;
import lombok.Setter;
import cn.cool.cherish.module.value.Value;

import java.util.ArrayList;
import java.util.List;

/**
 * Module
 * <AUTHOR>
 */
@Setter @Getter
public abstract class Module implements IMinecraft {
    private final List<Value> values = new ArrayList<>();

    private final Category category;
    private final String description;
    private final String name;
    private int keyBind;

    private boolean expanded;
    private boolean hidden;
    private boolean state;
    private String suffix;

    protected Module(String name, String description, Category category, int keyBind) {
        this.name = name;
        this.description = description;
        this.category = category;
        this.keyBind = keyBind;
    }

    protected Module(String name, String description, Category category) {
        this.name = name;
        this.description = description;
        this.category = category;
        this.keyBind = 0;
    }

    protected Module(String name, Category category) {
        this.name = name;
        this.category = category;
        this.description = "None";
        this.keyBind = 0;
    }

    public void setEnabled(boolean enabled) {
        if (this.state != enabled) {
            this.state = enabled;
            if (enabled) {
                enable();
            } else {
                disable();
            }
        }
    }

    public boolean isEnabled() {
        return state;
    }

    public <M extends Module> boolean isEnabled(Class<M> module) {
        Module mod = Client.instance.getModuleManager().getModule(module);
        return mod != null && mod.isEnabled();
    }

    public void toggle() {
        setEnabled(!isEnabled());
    }

    private void enable() {
        EventManager.register(this);

        try {
            onEnable();
        } catch (Exception e) {
            Client.logger.error(e.getMessage());
        }
    }

    private void disable() {
        EventManager.unregister(this);

        try {
            onDisable();
        } catch (Exception e) {
            Client.logger.error(e.getMessage());
        }
    }

    public <M extends Module> M getModule(Class<M> clazz) {
        return Client.instance.getModuleManager().getModule(clazz);
    }

    public Value getValue(String valueName) {
        return values.stream().filter(value -> value.getName().equalsIgnoreCase(valueName)).findFirst().orElse(null);
    }

    public void onEnable() {}

    public void onDisable() {}
}
