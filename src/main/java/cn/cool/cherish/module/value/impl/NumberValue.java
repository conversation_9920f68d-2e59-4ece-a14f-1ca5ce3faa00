package cn.cool.cherish.module.value.impl;

import lombok.Getter;
import cn.cool.cherish.module.value.Value;
import net.minecraft.util.Mth;

import java.util.function.Supplier;

@Getter
public class NumberValue extends Value {
    private final float min;
    private final float max;
    private final float inc;
    private float value;

    public NumberValue(String name, float value, float min, float max, float inc, Supplier<Boolean> visible) {
        super(name, visible);
        this.value = value;
        this.min = min;
        this.max = max;
        this.inc = inc;
    }

    public NumberValue(String name, float value, float min, float max, Supplier<Boolean> visible) {
        super(name, visible);
        this.value = value;
        this.min = min;
        this.max = max;
        this.inc = 1;
    }

    public NumberValue(String name, float value, float min, float max, float inc) {
        super(name, () -> true);
        this.value = value;
        this.min = min;
        this.max = max;
        this.inc = inc;
    }

    public NumberValue(String name, float value, float min, float max) {
        super(name, () -> true);
        this.value = value;
        this.min = min;
        this.max = max;
        this.inc = 1;
    }

    public float getValue() {
        return Mth.clamp(value, getMin(), getMax());
    }

    public void setValue(float value) {
        this.value = Mth.clamp(value, getMin(), getMax());
    }

}
