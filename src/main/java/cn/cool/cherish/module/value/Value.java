package cn.cool.cherish.module.value;

import lombok.Getter;

import java.awt.*;
import java.util.function.Supplier;

@Getter
public abstract class Value {
    private final String name;
    public Supplier<Boolean> visible;
    public Color color = Color.WHITE;

    public Value(String name, Supplier<Boolean> visible) {
        this.name = name;
        this.visible = visible;
    }

    public Boolean canDisplay() {
        return this.visible.get();
    }
}
