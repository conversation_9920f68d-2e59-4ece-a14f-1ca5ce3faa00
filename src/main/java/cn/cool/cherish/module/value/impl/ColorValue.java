package cn.cool.cherish.module.value.impl;

import cn.cool.cherish.utils.render.ColorUtil;
import lombok.Getter;
import lombok.Setter;
import cn.cool.cherish.module.value.Value;

import java.awt.*;
import java.util.function.Supplier;

@Setter @Getter
public class ColorValue extends Value {
    private float hue = 0;
    private float saturation = 1;
    private float brightness = 1;
    private float alpha = 1;
    private boolean rainbow = false;

    public ColorValue(String name, Color color) {
        super(name, () -> true);
        setValue(color);
    }

    public ColorValue(String name, Color color, Supplier<Boolean> visible) {
        super(name, visible);
        setValue(color);
    }

    public Color getValue() {
        if (rainbow) {
            return ColorUtil.applyOpacity(Color.getHSBColor((System.currentTimeMillis() % 3600) / 3600.0f, saturation, brightness), alpha);
        }
        return ColorUtil.applyOpacity(Color.getHSBColor(hue, saturation, brightness), alpha);
    }

    public void setValue(Color color) {
        float[] hsb = Color.RGBtoHSB(color.getRed(), color.getGreen(), color.getBlue(), null);
        hue = hsb[0];
        saturation = hsb[1];
        brightness = hsb[2];
        alpha = color.getAlpha() / 255.0f;
    }
}
