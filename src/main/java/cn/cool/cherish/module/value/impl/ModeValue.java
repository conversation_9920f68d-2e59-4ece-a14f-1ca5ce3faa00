package cn.cool.cherish.module.value.impl;

import lombok.Getter;
import lombok.Setter;
import cn.cool.cherish.module.value.Value;

import java.util.Arrays;
import java.util.function.Supplier;

@Getter @Setter
public class ModeValue extends Value {
    public String[] modes;
    private int index;

    public ModeValue(String name, String[] modes, String current, Supplier<Boolean> visible) {
        super(name, visible);
        this.modes = modes;
        this.index = Arrays.asList(modes).indexOf(current);
    }

    public ModeValue(String name, String[] modes, String current) {
        super(name, () -> true);
        this.modes = modes;
        this.index = Arrays.asList(modes).indexOf(current);
    }

    public ModeValue(String name, String[] modes) {
        super(name, () -> true);
        this.modes = modes;
        this.index = 0;
    }

    public boolean is(String mode) {
        return getValue().equals(mode);
    }

    public String getValue() {
        try {
            if (index < 0 || index >= modes.length) {
                return modes[0];
            }
            return modes[index];
        } catch (ArrayIndexOutOfBoundsException e) {
            return "ERROR";
        }
    }

    public void setValue(String mode) {
        int newIndex = Arrays.asList(modes).indexOf(mode);

        if (newIndex >= 0) {
            this.index = newIndex;
        }
    }

    public void setValue(int mode) {
        if (mode >= 0 && mode < modes.length) {
            this.index = mode;
        }
    }
}
