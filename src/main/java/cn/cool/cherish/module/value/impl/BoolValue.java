package cn.cool.cherish.module.value.impl;

import lombok.Setter;
import cn.cool.cherish.module.value.Value;

import java.util.function.Supplier;

@Setter
public class BoolValue extends Value {
    private boolean value;

    public BoolValue(String name, boolean value) {
        super(name, () -> true);
        this.value = value;
    }

    public BoolValue(String name, boolean value, Supplier<Boolean> visible) {
        super(name, visible);
        this.value = value;
    }

    public boolean getValue() {
        return value;
    }

    public void toggle() {
        value = !value;
    }
}
