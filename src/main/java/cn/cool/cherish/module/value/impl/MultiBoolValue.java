package cn.cool.cherish.module.value.impl;

import cn.cool.cherish.module.value.Value;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Supplier;
import java.util.stream.Collectors;

public class MultiBoolValue extends Value {
    public List<BoolValue> options;
    public int index;
    public float animation;

    public MultiBoolValue(String name, List<BoolValue> options) {
        super(name, () -> true);
        this.options = options;
        index = options.size();
    }

    public MultiBoolValue(String name, List<BoolValue> options, Supplier<Boolean> visible) {
        super(name, visible);
        this.options = options;
        index = options.size();
    }

    public boolean isEnabled(String name) {
        BoolValue option = this.options.stream()
                .filter(opt -> opt.getName().equalsIgnoreCase(name))
                .findFirst()
                .orElse(null);
        return option != null && option.getValue();
    }

    public String isEnabled() {
        List<String> includedOptions = new ArrayList<>();
        for (BoolValue option : options) {
            if (option.getValue()) {
                includedOptions.add(option.getName());
            }
        }
        return String.join(", ", includedOptions);
    }

    public boolean isEnabled(int index) {
        if (index >= 0 && index < this.options.size()) {
            return this.options.get(index).getValue();
        }
        return false;
    }

    public void setValue(int index, boolean value) {
        if (index >= 0 && index < this.options.size()) {
            this.options.get(index).setValue(value);
        }
    }

    public void setValue(String name, boolean value) {
        this.options.stream().filter(opt -> opt.getName().equalsIgnoreCase(name)).findFirst().ifPresent(option -> option.setValue(value));
    }

    public List<BoolValue> getValues() {
        return this.options;
    }

    public List<BoolValue> getToggled() {
        return this.options.stream().filter(BoolValue::getValue).collect(Collectors.toList());
    }
}
