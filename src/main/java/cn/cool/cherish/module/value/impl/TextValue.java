package cn.cool.cherish.module.value.impl;

import lombok.Getter;
import lombok.Setter;
import cn.cool.cherish.module.value.Value;

import java.util.function.Supplier;

@Setter @Getter
public class TextValue extends Value {
    private String text;
    private boolean isNumber;

    public TextValue(String name, String text) {
        super(name, () -> true);
        this.text = text;
        this.isNumber = false;
    }

    public TextValue(String name, String text, boolean isNumber, Supplier<Boolean> visible) {
        super(name, visible);
        this.text = text;
        this.isNumber = isNumber;
    }


    public TextValue(String name, String text, boolean isNumber) {
        super(name, () -> true);
        this.text = text;
        this.isNumber = isNumber;
    }

    public String getValue() {
        return text;
    }

    public void setValue(String text) {
        this.text = text;
    }
}
