package cn.cool.cherish.module;

import cn.cool.cherish.Client;
import cn.cool.cherish.evnet.EventManager;
import cn.cool.cherish.evnet.EventTarget;
import cn.cool.cherish.evnet.impl.input.KeyEvent;
import cn.cool.cherish.module.impl.movement.KeepSprint;
import cn.cool.cherish.module.impl.movement.Sprint;
import cn.cool.cherish.module.impl.player.NoSlowBreak;
import cn.cool.cherish.module.value.Value;
import lombok.Getter;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.lang.reflect.Field;
import java.util.*;

@Getter
public class ModuleManager {
    private final Collection<Module> modules = new TreeSet<>(Comparator.comparing(Module::getName));
    private final Map<Class<? extends Module>, Module> moduleMap = new HashMap<>(128);
    private final Map<Category, Set<Module>> categoryMap = new EnumMap<>(Category.class);

    public ModuleManager() {
        EventManager.register(this);

        for (Category category : Category.values()) {
            this.categoryMap.put(category, new TreeSet<>(Comparator.comparing(Module::getName)));
        }

        registerModule(
                Sprint.class,
                KeepSprint.class,
                NoSlowBreak.class
        );

        registerModuleValues();

        Client.logger.info("Registered {} modules.", modules.size());
    }

    @SafeVarargs
    public final void registerModule(Class<? extends Module>... moduleClasses) {
        for (final var moduleClass : moduleClasses) {
            try {
                Module module = moduleClass.getDeclaredConstructor().newInstance();

                modules.add(module);
                moduleMap.put(moduleClass, module);

                Category category = module.getCategory();
                this.categoryMap.get(category).add(module);
            } catch (Exception e) {
                Client.logger.error(e.getMessage());
            }
        }
    }

    private void registerModuleValues() {
        for (Module module : modules) {
            Class<?> moduleClass = module.getClass();
            Field[] fields = moduleClass.getDeclaredFields();

            for (Field field : fields) {
                if (Value.class.isAssignableFrom(field.getType())) {
                    try {
                        field.setAccessible(true);
                        Object fieldValue = field.get(module);
                        if (fieldValue instanceof Value value) {
                            module.getValues().add(value);
                        }
                    } catch (IllegalAccessException e) {
                        Client.logger.warn("Failed to access field {} in module {}",
                                field.getName(), module.getName());
                    }
                }
            }
        }
    }

    @SuppressWarnings("unchecked")
    public <T extends Module> T getModule(Class<T> moduleClass) {
        return (T) moduleMap.get(moduleClass);
    }

    @Nullable
    public Module getModuleByName(String name) {
        return modules.stream()
                .filter(m -> m.getName().equalsIgnoreCase(name))
                .findFirst()
                .orElse(null);
    }


    @NotNull
    public Set<Module> getModulesInCategory(Category category) {
        return this.categoryMap.get(category);
    }

    @NotNull
    public Collection<Module> getModules() {
        return List.copyOf(modules);
    }

    @EventTarget
    public void onKeyPress(KeyEvent event) {
        for (Module module : modules) {
            if (module.getKeyBind() == event.getKey()) {
                module.toggle();
            }
        }
    }
}
