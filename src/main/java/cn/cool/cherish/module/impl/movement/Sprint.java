package cn.cool.cherish.module.impl.movement;

import cn.cool.cherish.evnet.EventTarget;
import cn.cool.cherish.evnet.impl.player.TickEvent;
import cn.cool.cherish.evnet.impl.rander.EventSkiaProcess;
import cn.cool.cherish.module.Category;
import cn.cool.cherish.module.Module;
import cn.cool.cherish.utils.skija.SkiaRender;
import cn.cool.cherish.utils.skija.font.SkiaFontManager;
import net.minecraft.client.KeyMapping;

import java.awt.*;

public class Sprint extends Module {

    public Sprint() {
        super("Sprint", "Keep you running", Category.MOVEMENT);
        setEnabled(true);
    }

    @EventTarget
    public void onTick(TickEvent event) {
        KeyMapping.set(mc.options.keySprint.getKey(), true);
    }

    @Override
    public void onDisable() {
        KeyMapping.set(mc.options.keySprint.getKey(), mc.options.keySprint.isDown());
    }

    @EventTarget
    public void onSkia(EventSkiaProcess event) {
        SkiaRender.drawBlurRect(event.getCanvasStack(), 10, 10, 100, 50, 5, 2.0f);
        SkiaFontManager.getFont(18).drawText(event.getCanvasStack(), "Test", 10, 30, Color.WHITE.getRGB(), true);
    }
}