package cn.cool.cherish.module.impl.player;

import cn.cool.cherish.module.Category;
import cn.cool.cherish.module.Module;
import cn.cool.cherish.module.value.impl.BoolValue;

public class NoSlowBreak extends Module {
    public static NoSlowBreak instance;
    public final BoolValue miningFatigue = new BoolValue("Mining Fatigue", true);
    public final BoolValue onWater = new BoolValue("On Water", true);
    public final BoolValue onAir = new BoolValue("On Air", true);

    public NoSlowBreak() {
        super("NoSlowBreak", "Allows you to maintain normal destruction speed in some special cases", Category.PLAYER);
        instance = this;
    }
}
