plugins {
    id 'eclipse'
    id 'idea'
    id 'net.minecraftforge.gradle' version '[6.0.16,6.2)'
}


group = mod_group_id
version = mod_version

base {
    archivesName = mod_id
}

java {
    toolchain.languageVersion = JavaLanguageVersion.of(17)
}

minecraft {
    mappings channel: mapping_channel, version: mapping_version
    copyIdeResources = true

    runs {
        configureEach {
            workingDirectory project.file('run')
            args '--username', 'XiaTian233'
            property 'forge.logging.markers', 'REGISTRIES'
            property 'forge.logging.console.level', 'debug'

            mods {
                "${mod_id}" {
                    source sourceSets.main
                }
            }
        }

        client {
            property 'forge.enabledGameTestNamespaces', mod_id
        }

        server {
            property 'forge.enabledGameTestNamespaces', mod_id
            args '--nogui'
        }

        gameTestServer {
            property 'forge.enabledGameTestNamespaces', mod_id
        }

        data {
            workingDirectory project.file('run-data')
            args '--mod', mod_id, '--all', '--output', file('src/generated/resources/'), '--existing', file('src/main/resources/')
        }
    }
}

sourceSets.main.resources { srcDir 'src/generated/resources' }

repositories {}

dependencies {
    minecraft "net.minecraftforge:forge:${minecraft_version}-${forge_version}"

    // Lombok
    compileOnly 'org.projectlombok:lombok:1.18.34'
    implementation 'org.projectlombok:lombok:1.18.34'
    implementation 'org.openjdk.nashorn:nashorn-core:15.4'
    annotationProcessor 'org.projectlombok:lombok:1.18.34'
    testImplementation 'org.projectlombok:lombok:1.18.34'
    testAnnotationProcessor 'org.projectlombok:lombok:1.18.34'
    implementation 'org.openjdk.nashorn:nashorn-core:15.4'

    implementation fileTree(dir: 'libs', include: ['*.jar'])
}

tasks.named('processResources', ProcessResources).configure {
    var replaceProperties = [minecraft_version   : minecraft_version, minecraft_version_range: minecraft_version_range,
                             forge_version       : forge_version, forge_version_range: forge_version_range,
                             loader_version_range: loader_version_range,
                             mod_id              : mod_id, mod_name: mod_name, mod_license: mod_license, mod_version: mod_version,
                             mod_authors         : mod_authors, mod_description: mod_description,]

    inputs.properties replaceProperties

    filesMatching(['META-INF/mods.toml', 'pack.mcmeta']) {
        expand replaceProperties + [project: project]
    }
}

tasks.named('jar', Jar).configure {
    manifest {
        attributes(["Specification-Title"     : mod_id,
                    "Specification-Vendor"    : mod_authors,
                    "Specification-Version"   : "1", // We are version 1 of ourselves
                    "Implementation-Title"    : project.name,
                    "Implementation-Version"  : project.jar.archiveVersion,
                    "Implementation-Vendor"   : mod_authors,
                    "Implementation-Timestamp": new Date().format("yyyy-MM-dd'T'HH:mm:ssZ")])
    }

    finalizedBy 'reobfJar'
}

tasks.withType(JavaCompile).configureEach {
    options.encoding = 'UTF-8'
}

jar {
    manifest {
        attributes 'Premain-Class': 'cn.cool.cherish.Client'
        attributes 'Agent-Class': 'cn.cool.cherish.Client'
        attributes 'Can-Redefine-Classes': true, 'Can-Retransform-Classes': true, 'Can-Set-Native-Method-Prefix': true
    }
}

tasks.register('runCherish') {
    group = 'forgegradle runs'
    dependsOn build
    finalizedBy tasks.runClient
}
